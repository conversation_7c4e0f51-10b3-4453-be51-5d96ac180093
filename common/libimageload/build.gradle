apply from:"../../common_build.gradle"

dependencies {
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation ("io.coil-kt:coil:2.4.0"){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation("com.squareup.okio:okio:${prop_okio_version}")
    /*上面移除了OK，由于引入云同步ck，网络库使用内部taphttp，内含ok源码*/
    implementation("com.heytap.nearx:taphttp:${prop_taphttp_version}") {
        // 需移除 clientId，否则外销会有安规问题
        exclude group: 'com.heytap.baselib', module: 'clientId'
    }
    implementation project(':common:libbase')
    implementation project(':common:modulerouter')
}