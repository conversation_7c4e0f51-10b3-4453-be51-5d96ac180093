/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareAction
 * * Description: ShareAction
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.share

import android.app.Activity
import android.view.View
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.Constants
import kotlinx.coroutines.CoroutineScope

object ShareAction {
    private const val TAG = "ShareAction"
    const val COMPONENT_NAME = "ShareAction"
    const val FUN_SHARE = "share"
    const val SHOW_SHARE_LINK_PANEL = "showShareLinkPanel"
    const val CAN_UPLOAD_MORE_AUDIO_FILES = "canUploadMoreAudioFiles"
    const val FUN_REGISTER_SHARE_LISTENER = "registerShareListener"
    const val FUN_UNREGISTER_SHARE_LISTENER = "unregisterShareListener"

    // 未知错误
    const val ERROR_CODE_UNKNOWN = 0

    // 链接分享文本审核未通过
    const val ERROR_CODE_CONTENT_RISK = 1

    // 资源加载失败
    const val ERROR_CODE_RESOURCE_LOADING_FAILED = 2

    // 跳转外部应用失败
    const val ERROR_CODE_REDIRECT_FAILED = 3

    private val hasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    val dirOShare: String = getOShareDir()

    @JvmStatic
    val relativePathOShare: String = getOShareRelativePath()

    @JvmStatic
    val relativePathOShareText: String = getOShareTextRelativePath()

    @JvmStatic
    private fun getOShareDir(): String {
        if (BaseUtil.isRealme()) {
            return Constants.OPPO_SHARE_REALME_RECORDINGS
        }
        if (BaseUtil.isOnePlus()) {
            return Constants.OPPO_SHARE_ONE_PLUS_RECORDINGS
        }
        return Constants.OPPO_SHARE_RECORDINGS
    }

    @JvmStatic
    private fun getOShareRelativePath(): String {
        if (BaseUtil.isRealme()) {
            return Constants.OPPO_SHARE_REALME_RECORDINGS_RELATIVE_PATH
        }
        if (BaseUtil.isOnePlus()) {
            return Constants.OPPO_SHARE_ONE_PLUS_RECORDINGS_RELATIVE_PATH
        }
        return Constants.OPPO_SHARE_RECORDINGS_RELATIVE_PATH
    }

    @JvmStatic
    private fun getOShareTextRelativePath(): String {
        if (BaseUtil.isRealme()) {
            return Constants.OPPO_SHARE_REALME_RECORDINGS_TEXT
        }
        if (BaseUtil.isOnePlus()) {
            return Constants.OPPO_SHARE_ONE_PLUS_RECORDINGS_TEXT
        }
        return Constants.OPPO_SHARE_RECORDINGS_TEXT
    }

    @JvmStatic
    fun share(
        activity: Activity?,
        shareTextContent: ShareTextContent,
        type: ShareType,
        coroutineScope: CoroutineScope?,
        shareListener: IShareListener?
    ) {
        DebugUtil.d(TAG, "shareWithText hasComponent:$hasComponent")
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SHARE)
                .param(activity, shareTextContent, coroutineScope, shareListener, type)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        } else {
            DebugUtil.i(TAG, "shareWithText not has component")
        }
    }

    @JvmStatic
    fun registerShareListener(listener: IShareListener) {
        DebugUtil.d(TAG, "shareWithText hasComponent:$hasComponent")
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_REGISTER_SHARE_LISTENER)
                .param(listener)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        } else {
            DebugUtil.i(TAG, "shareWithText not has component")
        }
    }

    @JvmStatic
    fun unregisterShareListener(listener: IShareListener) {
        DebugUtil.d(TAG, "shareWithText hasComponent:$hasComponent")
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_UNREGISTER_SHARE_LISTENER)
                .param(listener)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        } else {
            DebugUtil.i(TAG, "shareWithText not has component")
        }
    }

    @JvmStatic
    fun showShareLinkPanel(activity: Activity, link: String, anchor: View?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SHOW_SHARE_LINK_PANEL)
                .param(activity, link, anchor)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        } else {
            DebugUtil.i(TAG, "showShareLinkPanel not has component")
        }
    }

    @JvmStatic
    fun canUploadMoreAudioFiles(): Boolean {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CAN_UPLOAD_MORE_AUDIO_FILES).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            DebugUtil.i(TAG, "canUploadMoreAudioFiles not has component")
            false
        }
    }
}