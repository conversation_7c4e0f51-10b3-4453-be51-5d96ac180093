/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordModeUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.AddonAdapterCompatUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.BaseUtil.isAndroidNOrLater
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.share.ShareAction
import com.soundrecorder.modulerouter.SettingAction
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import java.io.File
import java.util.Locale

private fun getRecordRootParentDir() = Environment.DIRECTORY_MUSIC

private fun getRecordRootDir(): String =
    getRecordRootParentDir() + File.separator + Constants.RECORDINGS

object RecordModeUtil {

    const val TAG = "RecordModeUtil"

    const val FRAGMENTS_TYPE_NORMAL = 0 // 标准模式
    const val FRAGMENTS_TYPE_MEETING = 1 // 会议模式
    const val FRAGMENTS_TYPE_INTERVIEW = 2 //采访模式
    const val FRAGMENTS_TYPE_INCALL_MULTIMODE = 3
    const val FRAGMENTS_TYPE_INCALL_NOMAL_MODE = 1
    const val FRAGMENTS_TYPE_THREEMODE_ALL = 4

    /* 虚拟化从设备类型，不支持通话录音 */
    const val VIRTUALCOMM_DEVICE_CONSUMER = 2
    const val LOCAL_RECORDMODE_SWITCH = "local_record_mode_switch"
    const val LOCAL_RECORDMODE_SWITCH_ENABLE = "local_record_mode_switch=enable"
    private const val SET_RECORD_MODE_STANDARD = "local_record_mode=standard"
    private const val SET_RECORD_MODE_CONFERENCE = "local_record_mode=conference"
    private const val SET_RECORD_MODE_INTERVIEW = "local_record_mode=interview"

    /*本地SP保存录制模式的KEY*/
    private const val PREFERENCE_KEY_RECORD_MODE = "record_mode"


    private val RECORD_TYPE_SUB_DIR_MAP = mapOf(
        RecordModeConstant.RECORD_TYPE_STANDARD to Constants.STANDARD_RECORDINGS,
        RecordModeConstant.RECORD_TYPE_CONFERENCE to Constants.MEETING_RECORDINGS,
        RecordModeConstant.RECORD_TYPE_INTERVIEW to Constants.INTERVIEW_RECORDINGS,
        RecordModeConstant.RECORD_TYPE_CALL to Constants.CALL_RECORDINGS,
        RecordModeConstant.RECORD_TYPE_OPPO_SHARE to ShareAction.dirOShare
    )

    /**
     * 是否支持多种录制默认
     * true：支持标准、会议、采访
     * false：支持标准
     */
    private var supportMultiMode: Boolean? = null



    /**
     * 获取用户设置录制模式项
     */
    @JvmStatic
    fun getModeValue(): Int =
        PrefUtil.getInt(
            BaseApplication.getAppContext(),
            PREFERENCE_KEY_RECORD_MODE,
            FRAGMENTS_TYPE_NORMAL
        )

    /**
     * 保存用户设置的录制模式项
     */
    @JvmStatic
    fun setModeValue(value: Int) {
        PrefUtil.putInt(BaseApplication.getAppContext(), PREFERENCE_KEY_RECORD_MODE, value)
    }

    @JvmStatic
    fun getRelativePathByRecordType(recordType: Int, includeLastSeparator: Boolean): String {

        val storeDir = StringBuffer()
        val subDir = RECORD_TYPE_SUB_DIR_MAP[recordType] ?: ""

        if (subDir.isEmpty()) {
            storeDir.append(getRecordRootParentDir())
        } else {
            if (RecordModeConstant.RECORD_TYPE_OPPO_SHARE == recordType) {
                storeDir.append(getRecordRootParentDir()).append(File.separator).append(subDir)
            } else {
                storeDir.append(getRecordRootDir()).append(File.separator).append(subDir)
            }
        }

        if (includeLastSeparator) {
            storeDir.append(File.separator)
        }
        return storeDir.toString()
    }

    @JvmStatic
    fun getRecordTypeForMediaRecord(record: Record?): Int {
        if (record == null) {
            return 0
        }
        var compareString: String? = null
        compareString = if (BaseUtil.isAndroidQOrLater) {
            record.relativePath
        } else {
            record.data
        }
        if (TextUtils.isEmpty(compareString)) {
            return 0
        }
        val storeDir = Constants.RECORDINGS
        val standRecordingPath = storeDir + File.separatorChar + Constants.STANDARD_RECORDINGS
        val meetingRecordingPath = storeDir + File.separatorChar + Constants.MEETING_RECORDINGS
        val interviewRecordingPath = storeDir + File.separatorChar + Constants.INTERVIEW_RECORDINGS
        val callRecoringPath = storeDir + File.separatorChar + Constants.CALL_RECORDINGS
        var outputType = 0
        if (compareString.contains(standRecordingPath)) {
            outputType = RecordModeConstant.RECORD_TYPE_STANDARD
        } else if (compareString.contains(meetingRecordingPath)) {
            outputType = RecordModeConstant.RECORD_TYPE_CONFERENCE
        } else if (compareString.contains(interviewRecordingPath)) {
            outputType = RecordModeConstant.RECORD_TYPE_INTERVIEW
        } else if (compareString.contains(callRecoringPath)) {
            outputType = RecordModeConstant.RECORD_TYPE_CALL
        }
        DebugUtil.i(
            TAG,
            "getRecordTypeForMediaRecord: compareString: $compareString, type: $outputType"
        )
        return outputType
    }

    @JvmStatic
    fun getBucketId(path: String): String? {
        return path.lowercase(Locale.US).hashCode().toString()
    }

    @JvmStatic
    fun checkSingleQuotes(text: String): String? {
        var text = text
        if (!TextUtils.isEmpty(text) && text.contains("'")) {
            text = text.replace("'".toRegex(), "''")
        }
        return text
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun isNeedHideRecord(context: Context?): Boolean {
        if (context == null) {
            DebugUtil.d(TAG, "isNeedHideRecord context is null return")
            return false
        }
        val isInCallUIinstalled: Boolean = BaseUtil.isOplusIncalluiEnabledForExp(context)
        if (!isInCallUIinstalled) {
            DebugUtil.d(TAG, "isInCallUIinstalled false")
            return true
        }
        if (virtualcommDeviceType(context)) {
            DebugUtil.d(TAG, "is virtualcommDevice true")
            return true
        }
        try {
            val hasFeature = FeatureOption.OPLUS_PHONE_NODISPLAYRECORD
            val clazz: Class<*> =
                BaseUtil::class.java.getClassLoader().loadClass("android.os.SystemProperties")
            val method = clazz.getMethod("get", String::class.java, String::class.java)
            val simNumeric = method.invoke(null, "gsm.sim.operator.numeric", "") as String
            DebugUtil.d(TAG, "isNeedHideRecord  hasFeature = $hasFeature")
            return if (hasFeature || (simNumeric.startsWith("505")
                        || simNumeric.startsWith("310")
                        || simNumeric.startsWith("311"))
            ) {
                DebugUtil.d(TAG, "isNeedHideRecord true")
                true
            } else {
                false
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "isNeedHideRecord error:" + e.message)
        }
        return false
    }

    /**
     * 是否支持通话或者包含支持三方应用通话录音功能入口
     */
    @JvmStatic
    fun hasCallRecording(context: Context?): Boolean {
        return !isNeedHideRecord(context) || SettingAction.isSupportTripartiteAudioMonitor()
    }

    /**
     * 是否是虚拟化从设备（支持通话，但不支持通话录音）
     * 虚拟化从设备的type类型为2
     */
    @JvmStatic
    fun virtualcommDeviceType(context: Context): Boolean {
        kotlin.runCatching {
            val virtualcommDeviceType = AddonAdapterCompatUtil.getVirtualcommDeviceType(context)
            DebugUtil.d(TAG, "virtualcommDeviceType = $virtualcommDeviceType")
            return virtualcommDeviceType == VIRTUALCOMM_DEVICE_CONSUMER
        }.onFailure {
            DebugUtil.e(TAG, "get virtualcommDeviceType error:" + it.message)
        }
        return false
    }

    @JvmStatic
    fun isSupportMultiRecordMode(context: Context): Boolean {
        if (supportMultiMode != null) {
            return supportMultiMode!!
        }
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as? AudioManager?
        supportMultiMode = if (audioManager != null) {
            val recordModeSwitch = audioManager.getParameters(LOCAL_RECORDMODE_SWITCH)
            if (!TextUtils.isEmpty(recordModeSwitch) && recordModeSwitch == LOCAL_RECORDMODE_SWITCH_ENABLE) {
                DebugUtil.i(TAG, "isSupportThreeRecordMode = true")
                true
            } else {
                DebugUtil.i(TAG, "recordModeSwitch : $recordModeSwitch")
                false
            }
        } else {
            DebugUtil.i(TAG, "AudioManager  : null")
            false
        }
        return supportMultiMode ?: false
    }

    @JvmStatic
    fun String?.cleanRelativePath(): String? {
        var cleanRelativePath = this

        if (!BaseUtil.isAndroidQOrLater) {
            return cleanRelativePath
        }
        val hasEndSeparator = this?.endsWith(File.separator) == true
        if (hasEndSeparator) {
            cleanRelativePath = this?.substring(0, length - 1)
        }

        cleanRelativePath = when {
            RecordModeConstant.RELATIVE_PATH_RECORDINGS_NO_SLASH.equals(
                cleanRelativePath,
                true
            ) -> RecordModeConstant.RELATIVE_PATH_RECORDINGS_NO_SLASH

            RecordModeConstant.RELATIVE_PATH_STANDARD_NO_SLASH.equals(
                cleanRelativePath,
                true
            ) -> RecordModeConstant.RELATIVE_PATH_STANDARD_NO_SLASH

            RecordModeConstant.RELATIVE_PATH_MEETING_NO_SLASH.equals(
                cleanRelativePath,
                true
            ) -> RecordModeConstant.RELATIVE_PATH_MEETING_NO_SLASH

            RecordModeConstant.RELATIVE_PATH_INTERVIEW_NO_SLASH.equals(
                cleanRelativePath,
                true
            ) -> RecordModeConstant.RELATIVE_PATH_INTERVIEW_NO_SLASH

            RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH.equals(
                cleanRelativePath,
                true
            ) -> RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH

            RecordModeConstant.RELATIVE_PATH_OSHARE_NO_SLASH.equals(
                cleanRelativePath,
                true
            ) -> RecordModeConstant.RELATIVE_PATH_OSHARE_NO_SLASH

            else -> cleanRelativePath
        }

        if (hasEndSeparator) {
            cleanRelativePath += File.separator
        }

        return cleanRelativePath
    }

    @JvmStatic
    fun String?.recordType(): Int {
        var relativePath = this

        val hasEndSeparator = this?.endsWith(File.separator) == true
        if (hasEndSeparator) {
            relativePath = this?.substring(0, length - 1)
        }
        relativePath = relativePath.cleanRelativePath()
        return when (relativePath) {
            RecordModeConstant.RELATIVE_PATH_RECORDINGS_NO_SLASH,
            RecordModeConstant.RELATIVE_PATH_STANDARD_NO_SLASH,
            RecordModeConstant.RELATIVE_PATH_OSHARE_NO_SLASH,
            -> RecordModeConstant.RECORD_TYPE_STANDARD

            RecordModeConstant.RELATIVE_PATH_MEETING_NO_SLASH -> RecordModeConstant.RECORD_TYPE_CONFERENCE
            RecordModeConstant.RELATIVE_PATH_INTERVIEW_NO_SLASH -> RecordModeConstant.RECORD_TYPE_INTERVIEW
            RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH -> RecordModeConstant.RECORD_TYPE_CALL
            else -> RecordModeConstant.RECORD_TYPE_OTHER
        }
    }

    @JvmStatic
    fun containRecordRelativePath(data: String): Boolean {
        var containRecorderFolders = false
        if (TextUtils.isEmpty(data)) {
            return containRecorderFolders
        }
        val standardRelativePath =
            getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_STANDARD, false)
        val interviewRelativePath =
            getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_INTERVIEW, false)
        val meetingRelativePath =
            getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_CONFERENCE, false)
        val callRelativePath =
            getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_CALL, false)
        containRecorderFolders =
            data.contains(standardRelativePath, true)
                    || data.contains(interviewRelativePath, true)
                    || data.contains(meetingRelativePath, true)
                    || data.contains(callRelativePath, true)
        DebugUtil.i(TAG, "containRecordRelativePath: $containRecorderFolders")
        return containRecorderFolders
    }

    @JvmStatic
    fun ensureFoldersExist(mContext: Context) {
        var parentRelativePath =
            StorageManager.getInstance(mContext).storagePrefix + File.separatorChar
        parentRelativePath += getRecordRootDir()

        DebugUtil.i(TAG, "parentRelativePath: $parentRelativePath")
        val parentRelativeFolder = File(parentRelativePath)
        if (!parentRelativeFolder.exists()) {
            parentRelativeFolder.mkdir()
            DebugUtil.i(TAG, "parent folder is not exist, mkdir")
        }
        val normalRelativePath =
            parentRelativePath + File.separatorChar + mContext.getString(R.string.normal_store_dir)
        val normalRelativeFolder = File(normalRelativePath)
        if (!normalRelativeFolder.exists()) {
            normalRelativeFolder.mkdir()
            DebugUtil.i(TAG, "standard folder is not exist, mkdir")
        }
        val needCheckCallingFolder = isAndroidNOrLater && hasCallRecording(mContext)
        if (needCheckCallingFolder) {
            val callingRelativePath =
                parentRelativePath + File.separatorChar + mContext.getString(R.string.calling_store_dir)
            val callingRelativeFolder = File(callingRelativePath)
            if (!callingRelativeFolder.exists()) {
                callingRelativeFolder.mkdir()
                DebugUtil.i(TAG, "calling folder is not exist,  mkdir")
            }
        }
        val needCheckInterViewAndMeetingFolder = isSupportMultiRecordMode(mContext)
        if (needCheckInterViewAndMeetingFolder) {
            val interviewRelativePath =
                parentRelativePath + File.separatorChar + mContext.getString(R.string.interview_store_dir)
            val interviewRelativeFolder = File(interviewRelativePath)
            if (!interviewRelativeFolder.exists()) {
                DebugUtil.i(TAG, "interview folder is not exist, mkdir")
                interviewRelativeFolder.mkdir()
            }
            val meetingRelativePath =
                parentRelativePath + File.separatorChar + mContext.getString(R.string.meeting_store_dir)
            val meetingRelativeFolder = File(meetingRelativePath)
            if (!meetingRelativeFolder.exists()) {
                DebugUtil.i(TAG, "meeting folder is not exist, mkdir")
                meetingRelativeFolder.mkdir()
            }
        }
    }

    @JvmStatic
    fun checkFoldersNotExist(mContext: Context): Boolean {
        var isNotExist = false
        var parentRelativePath =
            StorageManager.getInstance(mContext).storagePrefix + File.separatorChar
        parentRelativePath += getRecordRootDir()

        DebugUtil.i(TAG, "parentRelativePath: $parentRelativePath")
        val parentRelativeFolder = File(parentRelativePath)
        if (!parentRelativeFolder.exists()) {
            isNotExist = true
            return isNotExist
        }
        val normalRelativePath =
            parentRelativePath + File.separatorChar + mContext.getString(R.string.normal_store_dir)
        val normalRelativeFolder = File(normalRelativePath)
        DebugUtil.i(TAG, "normalRelativePath: $normalRelativePath")
        if (!normalRelativeFolder.exists()) {
            isNotExist = true
            return isNotExist
        }
        val needCheckCallingFolder = isAndroidNOrLater && hasCallRecording(mContext)
        if (needCheckCallingFolder) {
            val callingRelativePath =
                parentRelativePath + File.separatorChar + mContext.getString(R.string.calling_store_dir)
            val callingRelativeFolder = File(callingRelativePath)
            if (!callingRelativeFolder.exists()) {
                DebugUtil.i(TAG, "calling folder is not exist")
                isNotExist = true
                return isNotExist
            }
        }
        val needCheckInterViewAndMeetingFolder = isSupportMultiRecordMode(mContext)
        if (needCheckInterViewAndMeetingFolder) {
            val interviewRelativePath =
                parentRelativePath + File.separatorChar + mContext.getString(R.string.interview_store_dir)
            val interviewRelativeFolder = File(interviewRelativePath)
            if (!interviewRelativeFolder.exists()) {
                DebugUtil.i(TAG, "interview folder is not exist")
                isNotExist = true
                return isNotExist
            }
            val meetingRelativePath =
                parentRelativePath + File.separatorChar + mContext.getString(R.string.meeting_store_dir)
            val meetingRelativeFolder = File(meetingRelativePath)
            if (!meetingRelativeFolder.exists()) {
                DebugUtil.i(TAG, "meeting folder is not exist")
                isNotExist = true
                return isNotExist
            }
        }
        return isNotExist
    }

    @JvmStatic
    fun setRecordMode(context: Context, mRecordType: Int): Boolean {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
        if (audioManager != null) {
            val recordModeSwitch = audioManager.getParameters(LOCAL_RECORDMODE_SWITCH)
            if (!TextUtils.isEmpty(recordModeSwitch) && recordModeSwitch == LOCAL_RECORDMODE_SWITCH_ENABLE) {
                DebugUtil.i(
                    TAG,
                    "isSupportThreeRecordMode = true input mRecordType is $mRecordType"
                )
                var mode = ""
                mode = when (mRecordType) {
                    // set record mode to standard
                    FRAGMENTS_TYPE_NORMAL -> SET_RECORD_MODE_STANDARD
                    // set recording mode to conference
                    FRAGMENTS_TYPE_MEETING -> SET_RECORD_MODE_CONFERENCE
                    // set recording mode to interview
                    FRAGMENTS_TYPE_INTERVIEW -> SET_RECORD_MODE_INTERVIEW
                    else -> {
                        DebugUtil.i(
                            TAG,
                            "input mRecordType $mRecordType is not support mode"
                        )
                        return false
                    }
                }
                if (!TextUtils.isEmpty(mode)) {
                    audioManager.setParameters(mode)
                    DebugUtil.i(TAG, "setParameters : $mode")
                    return true
                }
            } else {
                DebugUtil.i(TAG, "recordModeSwitch : $recordModeSwitch")
            }
        } else {
            DebugUtil.i(TAG, "setRecordMode audioManager == null !")
        }
        return false
    }



    @JvmStatic
    fun Intent?.isFromMessage(): Boolean {
        return when (this?.action) {
            Intent.ACTION_GET_CONTENT,
            Intent.ACTION_PICK,
            MediaStore.Audio.Media.RECORD_SOUND_ACTION,
            -> true

            else -> false
        }
    }

    @JvmStatic
    fun Intent?.isFromCall(): Boolean {
        return when (this?.action) {
            OplusCompactConstant.START_BROWSE_ACTION_BEFOR,
            OplusCompactConstant.START_BROWSE_ACTION_AFTER,
            -> true

            else -> false
        }
    }

    /**
     * 三方应用通话录音跳转过来，是否切换到通话录音tab
     * 未传递路径参数默认跳转到call tab，传递后则判断是否是通话路径（适用于15.0全局语音摘要）
     */
    @JvmStatic
    fun Intent?.isThreeRecordJumpToCall(): Boolean {
        return when (this?.action) {
            OplusCompactConstant.START_BROWSE_ACTION_THREAD_RECORD -> {
                val jumpPath = this.getStringExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_PATH)
                if (jumpPath.isNullOrEmpty()) {
                    true
                } else {
                    jumpPath.endsWith(RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH)
                }
            }

            else -> false
        }
    }

    @JvmStatic
    fun Intent?.isFromOther(): Boolean {
        return isFromCall() || isFromMessage()
    }
}