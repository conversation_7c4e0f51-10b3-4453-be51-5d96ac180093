/*********************************************************************
 * Copyright (C), 2024-2034 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : COUIAnimateSpan2
 * Description :
 * Version     : 1.0
 * Date        : 2024/11/14 14:46
 * Author      : 80226902
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * 80226902        2024/7/17       1.0      create
 **/
package com.soundrecorder.common.widget.animspan

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.SystemClock
import android.view.animation.PathInterpolator

@Suppress("MagicNumber")
class COUIAnimateSpan2(param: COUIAnimateSpanParam) : COUIAnimateSpan(param) {

    companion object {
        const val D_ONE = 1.0f
        const val FLOAT_16: Float = 0.16667f
        private val INTERPOLATOR = PathInterpolator(0.17f, 0.17f, 0.83f, 0.83f)
        private const val UNSET = -1L
        private const val ALPHA_START = 0.44445f
        private const val DURATION_DEFAULT = 420L
    }

    private val mTextSize: Float = param.textSize
    private val mStableColor: Int = param.stableColor
    private var mDuration: Long = if (param.duration < DURATION_DEFAULT) DURATION_DEFAULT else param.duration
    private var mDelay: Long = param.delay
    private var mOffset: Float = param.offset
    private val mEndRunnable: Runnable = param.rundRunnable
    private var fraction = 0f

    private val color1Start = Color.parseColor("#29E549")
    private val color1End = Color.parseColor("#FFC219")

    private val color2Start = color1End
    private val color2End = Color.parseColor("#FF6536")

    private val color3Start = color2End
    private val color3End = Color.parseColor("#C25DFF")

    private val color4Start = color3End
    private val color4End = Color.parseColor("#4A86FF")

    private val color5Start = color4End
    private val color5End = Color.parseColor("#10BFF7")

    private val color6Start = color5End
    private val color6End = mStableColor

    private val mAlphaEnd = Color.alpha(mStableColor)

    override fun draw(
        canvas: Canvas,
        text: CharSequence,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        val drawText = text.subSequence(start, end).toString()
        if (mStartTime != UNSET) {
            val deltaTime = SystemClock.uptimeMillis() - mStartTime - mDelay
            if (deltaTime <= 0) {
                fraction = 0f
            } else if (deltaTime >= mDuration) {
                fraction = 1f
                mStartTime = UNSET
                mEndRunnable.run()
            } else {
                fraction = getInterpolation((deltaTime * D_ONE / mDuration))
            }
            if (mDelay != 0L && fraction == 0f && ((mAnimateType and TYPE_SEQUENCE) != 0)) {
                return
            }
        }
        paint.textSize = mTextSize
        paint.color = getColor(fraction)
        paint.alpha = getAlpha(fraction)
        val offsetY = getOffsetY(fraction, y)
        canvas.drawText(drawText, x, offsetY, paint)
    }

    private fun getInterpolation(fraction: Float): Float {
        if (fraction <= 0 || fraction >= 1) {
            return 1f
        }
        return INTERPOLATOR.getInterpolation(fraction)
    }

    private fun getOffsetY(fraction: Float, y: Int): Float {
        return if (((mAnimateType and TYPE_OFFSETY) != 0)) {
            (y + (1f - fraction) * mOffset)
        } else {
            y.toFloat()
        }
    }

    private fun getColor(fraction: Float): Int {
        if (fraction <= 0 || fraction >= 1f || ((mAnimateType and TYPE_GRADIENT) == 0)) {
            return mStableColor
        }
        return if (fraction <= FLOAT_16) {
            evaluate(fraction * 6f, color1Start, color1End) as Int
        } else if (fraction <= FLOAT_16 * 2) {
            evaluate((fraction - FLOAT_16) * 6f, color2Start, color2End) as Int
        } else if (fraction <= FLOAT_16 * 3) {
            evaluate((fraction - FLOAT_16 * 2) * 6f, color3Start, color3End) as Int
        } else if (fraction <= FLOAT_16 * 4) {
            evaluate((fraction - FLOAT_16 * 3) * 6f, color4Start, color4End) as Int
        } else if (fraction <= FLOAT_16 * 5) {
            evaluate((fraction - FLOAT_16 * 4) * 6f, color5Start, color5End) as Int
        } else {
            evaluate((fraction - FLOAT_16 * 5) * 6f, color6Start, color6End) as Int
        }
    }

    private fun getAlpha(fraction: Float): Int {
        if (fraction <= 0 || fraction >= 1) {
            return mAlphaEnd
        }
        return if (fraction <= FLOAT_16) {
            (ALPHA_START * fraction * 6 * mAlphaEnd).toInt()
        } else if (fraction <= FLOAT_16 * 4) {
            (ALPHA_START * mAlphaEnd).toInt()
        } else if (fraction <= FLOAT_16 * 5) {
            val f = fraction - FLOAT_16 * 4
            ((ALPHA_START + ((1f - ALPHA_START) * f * 6)) * mAlphaEnd).toInt()
        } else {
            mAlphaEnd
        }
    }
}