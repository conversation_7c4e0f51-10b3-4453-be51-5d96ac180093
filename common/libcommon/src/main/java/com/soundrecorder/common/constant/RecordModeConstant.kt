/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordModeConstant
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.constant

import com.soundrecorder.common.share.ShareAction
import java.io.File

object RecordModeConstant {
    const val SEPARATOR: String = "/"
    const val RELATIVE_PATH_BASE: String = "Music/Recordings$SEPARATOR"

    const val RELATIVE_PATH_RECORDINGS_NO_SLASH: String = "Music/Recordings"
    const val RELATIVE_PATH_STANDARD_NO_SLASH: String = "${RELATIVE_PATH_BASE}Standard Recordings"
    const val RELATIVE_PATH_MEETING_NO_SLASH: String = "${RELATIVE_PATH_BASE}Meeting Recordings"
    const val RELATIVE_PATH_INTERVIEW_NO_SLASH: String = "${RELATIVE_PATH_BASE}Interview Recordings"
    const val RELATIVE_PATH_CALL_NO_SLASH: String = "${RELATIVE_PATH_BASE}Call Recordings"
    val RELATIVE_PATH_OSHARE_NO_SLASH: String = ShareAction.dirOShare

    const val RELATIVE_PATH_STANDARD: String = "$RELATIVE_PATH_STANDARD_NO_SLASH$SEPARATOR"
    const val RELATIVE_PATH_MEETING: String = "$RELATIVE_PATH_MEETING_NO_SLASH$SEPARATOR"
    const val RELATIVE_PATH_INTERVIEW: String = "$RELATIVE_PATH_INTERVIEW_NO_SLASH$SEPARATOR"
    const val RELATIVE_PATH_CALL: String = "$RELATIVE_PATH_CALL_NO_SLASH$SEPARATOR"
    val RELATIVE_PATH_OPPO_SHARE: String = ShareAction.relativePathOShare

    // ONE_PLUS Record folders
    const val OP_RECORD = "Record"
    const val OP_RELATIVE_PATH_RECORD_ABOVE_Q = "Music/Record"
    const val OP_STORAGE_RECORD_ABOVE_AND_R = "Music/Record/SoundRecord"
    const val OP_STORAGE_RECORD_BELOW_Q = "Record/SoundRecord"

    const val STORAGE_RECORD = "Recordings/"
    const val STORAGE_RECORD_ABOVE_Q = "Music/Recordings/"
    const val DIR_STANDARD = "Standard Recordings"
    val DIR_STANDARD_END = File.separator + "Standard Recordings" + File.separator
    const val DIR_MEETING = "Meeting Recordings"
    val DIR_MEETING_END = File.separator + "Meeting Recordings" + File.separator
    const val DIR_INTERVIEW = "Interview Recordings"
    val DIR_INTERVIEW_END = File.separator + "Interview Recordings" + File.separator
    const val DIR_CALL = "Call Recordings"
    val DIR_CALL_END = File.separator + "Call Recordings" + File.separator
    val DIR_OPPO_SHARE = "Music$SEPARATOR${ShareAction.dirOShare}"
    val DIR_OPPO_SHARE_END = File.separator + DIR_OPPO_SHARE + File.separator

    const val RECORD_TYPE_STANDARD = 0
    const val RECORD_TYPE_CONFERENCE = 1
    const val RECORD_TYPE_INTERVIEW = 2
    const val RECORD_TYPE_CALL = 3
    const val RECORD_TYPE_OTHER = 5 // TO recorder files
    const val RECORD_TYPE_OPPO_SHARE = 6
    val ALL_RECORD_TYPE_LIST = listOf(
        RECORD_TYPE_STANDARD,
        RECORD_TYPE_CONFERENCE,
        RECORD_TYPE_INTERVIEW,
        RECORD_TYPE_CALL,
        RECORD_TYPE_OTHER,
        RECORD_TYPE_OPPO_SHARE,
    )

    /*全部录音*/
    const val BUCKET_VALUE_ALL = 0
    const val BUCKET_VALUE_STANDARD = 1
    const val BUCKET_VALUE_INTERVIEW = 2
    const val BUCKET_VALUE_CONFERENCE = 3
    /*通话录音*/
    const val BUCKET_VALUE_CALL = 4
    /*最近删除*/
    const val BUCKET_VALUE_RECENTLY_DELETED = 5
}