package com.soundrecorder.common.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import java.util.List;

/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/5/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class RecordFileChangeNotifyTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnEmpty_when_init() {
        RecordFileChangeNotify notify = new RecordFileChangeNotify();
        notify.init();
        List<Integer> mRecorderTypes = Whitebox.getInternalState(notify, "mRecorderTypes");
        assertEquals(0, mRecorderTypes.size());
    }

    @Test
    public void should_returnNotEmpty_when_setFileChanged() {
        RecordFileChangeNotify notify = new RecordFileChangeNotify();
        notify.setFileChanged(RecordModeConstant.RECORD_TYPE_STANDARD);
        List<Integer> mRecorderTypes = Whitebox.getInternalState(notify, "mRecorderTypes");
        assertFalse(mRecorderTypes.isEmpty());
    }

    @Test
    public void should_returnNotEmpty_when_notifyBySendBroadcast() {
        RecordFileChangeNotify notify = new RecordFileChangeNotify();
        Whitebox.setInternalState(notify, "mFilesHasChanged", true);
        notify.notifyBySendBroadcast(mContext);
        boolean mFilesHasChanged = Whitebox.getInternalState(notify, "mFilesHasChanged");
        assertFalse(mFilesHasChanged);
    }
}
