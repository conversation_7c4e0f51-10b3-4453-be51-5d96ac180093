/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : CursorHelperRobolectricTestRunnerTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/8/30
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/8/30, LI Kun, create
 ************************************************************/

package com.soundrecorder.common.db;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.shadows.ShadowBaseUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowBaseUtils.class, ShadowFeatureOption.class})
public class CursorHelperRobolectricTestRunnerTest {

    private static final String STORAGE_EMULATED = "/storage/emulated";
    private static final String TEST_SDCARD_DIR = "test/sdcardDir";
    private static final String PROJECTION_Q = "PROJECTION_Q";
    private Context mContext;
    private CursorHelper mCursorHelper;

    @Before
    public void setUp() {
        mCursorHelper = new CursorHelper();
        mContext = ApplicationProvider.getApplicationContext();
    }

    /*@Test
    public void should_returnStringPath_when_getAllRecordWhereClause_with_isAndroidQOrLaterIsTrue() {
        String result = CursorHelper.getAllRecordWhereClause(mContext);
        Assert.assertFalse(result.contains(STORAGE_EMULATED));
    }*/

    @Test
    public void should_returnStringPath_when_getCallRecordWhereClause_with_isAndroidQOrLater() {
        String result = CursorHelper.getCallRecordWhereClause(mContext);
        Assert.assertFalse(result.contains(STORAGE_EMULATED));
        Assert.assertFalse(result.contains(TEST_SDCARD_DIR));
    }

    @Test
    public void should_returnProjection_when_getProjection_with_isAndroidQOrLaterTrue() {
        String[] result = CursorHelper.getProjection();
        String[] projection = Whitebox.getInternalState(CursorHelper.class, PROJECTION_Q);
        Assert.assertEquals(projection, result);
    }
}
