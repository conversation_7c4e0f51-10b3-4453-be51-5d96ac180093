/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseWindowCallbackTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.dialog

import android.os.Build
import android.view.ActionMode
import android.view.KeyEvent
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.SearchEvent
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class BaseWindowCallbackTest {
    companion object {
        private const val METHOD_DISPATCH_KEY_EVENT = "dispatchKeyEvent"
        private const val METHOD_DISPATCH_SHORTCUT_EVENT = "dispatchKeyShortcutEvent"
        private const val METHOD_DISPATCH_TOUCH_EVENT = "dispatchTouchEvent"
        private const val METHOD_DISPATCH_TRACK_BALL_EVENT = "dispatchTrackballEvent"
        private const val METHOD_DISPATCH_MOTION_EVENT = "dispatchGenericMotionEvent"
        private const val METHOD_DISPATCH_POP_ACCESS = "dispatchPopulateAccessibilityEvent"
        private const val METHOD_ON_CREATE_PANEL_VIEW = "onCreatePanelView"
        private const val METHOD_ON_CREATE_PANEL_MENU = "onCreatePanelMenu"
        private const val METHOD_ON_PREPARE_PANEL = "onPreparePanel"
        private const val METHOD_ON_MENU_OPEN = "onMenuOpened"
        private const val METHOD_ON_MENU_SEL = "onMenuItemSelected"
        private const val METHOD_ON_WINDOW_ATT_CHANGED = "onWindowAttributesChanged"
        private const val METHOD_ON_CONTENT_CHANGED = "onContentChanged"
        private const val METHOD_ON_WINDOW_FOCUS_CHANGED = "onWindowFocusChanged"
        private const val METHOD_ON_ATTACHED_TO_WINDOW = "onAttachedToWindow"
        private const val METHOD_ON_DETACHED_FROM_WINDOW = "onDetachedFromWindow"
        private const val METHOD_ON_PANEL_CLOSED = "onPanelClosed"
        private const val METHOD_ON_SEARCH_REQUEST = "onSearchRequested"
        private const val METHOD_ON_SEARCH_REQUEST1 = "onSearchRequested1"
        private const val METHOD_ON_WINDOW_START_ACTION_MODE = "onWindowStartingActionMode"
        private const val METHOD_ON_WINDOW_START_ACTION_MODE1 = "onWindowStartingActionMode1"
        private const val METHOD_ON_ACTION_MODE_STARTED = "onActionModeStarted"
        private const val METHOD_ON_ACTION_MODE_FINISHED = "onActionModeFinished"
    }

    @Test
    fun should_success_when_callback() {
        val methodCall = mutableMapOf<String, Boolean>()
        val callback = object : Window.Callback {
            override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
                methodCall[METHOD_DISPATCH_KEY_EVENT] = true
                return true
            }

            override fun dispatchKeyShortcutEvent(event: KeyEvent?): Boolean {
                methodCall[METHOD_DISPATCH_SHORTCUT_EVENT] = true
                return true
            }

            override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
                methodCall[METHOD_DISPATCH_TOUCH_EVENT] = true
                return true
            }

            override fun dispatchTrackballEvent(event: MotionEvent?): Boolean {
                methodCall[METHOD_DISPATCH_TRACK_BALL_EVENT] = true
                return true
            }

            override fun dispatchGenericMotionEvent(event: MotionEvent?): Boolean {
                methodCall[METHOD_DISPATCH_MOTION_EVENT] = true
                return true
            }

            override fun dispatchPopulateAccessibilityEvent(event: AccessibilityEvent?): Boolean {
                methodCall[METHOD_DISPATCH_POP_ACCESS] = true
                return true
            }

            override fun onCreatePanelView(featureId: Int): View? {
                methodCall[METHOD_ON_CREATE_PANEL_VIEW] = true
                return null
            }

            override fun onCreatePanelMenu(featureId: Int, menu: Menu): Boolean {
                methodCall[METHOD_ON_CREATE_PANEL_MENU] = true
                return true
            }

            override fun onPreparePanel(featureId: Int, view: View?, menu: Menu): Boolean {
                methodCall[METHOD_ON_PREPARE_PANEL] = true
                return true
            }

            override fun onMenuOpened(featureId: Int, menu: Menu): Boolean {
                methodCall[METHOD_ON_MENU_OPEN] = true
                return true
            }

            override fun onMenuItemSelected(featureId: Int, item: MenuItem): Boolean {
                methodCall[METHOD_ON_MENU_SEL] = true
                return true
            }

            override fun onWindowAttributesChanged(attrs: WindowManager.LayoutParams?) {
                methodCall[METHOD_ON_WINDOW_ATT_CHANGED] = true
            }

            override fun onContentChanged() {
                methodCall[METHOD_ON_CONTENT_CHANGED] = true
            }

            override fun onWindowFocusChanged(hasFocus: Boolean) {
                methodCall[METHOD_ON_WINDOW_FOCUS_CHANGED] = true
            }

            override fun onAttachedToWindow() {
                methodCall[METHOD_ON_ATTACHED_TO_WINDOW] = true
            }

            override fun onDetachedFromWindow() {
                methodCall[METHOD_ON_DETACHED_FROM_WINDOW] = true
            }

            override fun onPanelClosed(featureId: Int, menu: Menu) {
                methodCall[METHOD_ON_PANEL_CLOSED] = true
            }

            override fun onSearchRequested(): Boolean {
                methodCall[METHOD_ON_SEARCH_REQUEST] = true
                return true
            }

            override fun onSearchRequested(searchEvent: SearchEvent?): Boolean {
                methodCall[METHOD_ON_SEARCH_REQUEST1] = true
                return true
            }

            override fun onWindowStartingActionMode(callback: ActionMode.Callback?): ActionMode? {
                methodCall[METHOD_ON_WINDOW_START_ACTION_MODE] = true
                return null
            }

            override fun onWindowStartingActionMode(
                callback: ActionMode.Callback?,
                type: Int
            ): ActionMode? {
                methodCall[METHOD_ON_WINDOW_START_ACTION_MODE1] = true
                return null
            }

            override fun onActionModeStarted(mode: ActionMode?) {
                methodCall[METHOD_ON_ACTION_MODE_STARTED] = true
            }

            override fun onActionModeFinished(mode: ActionMode?) {
                methodCall[METHOD_ON_ACTION_MODE_FINISHED] = true
            }
        }
        val windowCallback = object : BaseWindowCallback(callback) {}
        windowCallback.dispatchKeyEvent(Mockito.mock(KeyEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_DISPATCH_KEY_EVENT])

        windowCallback.dispatchKeyShortcutEvent(Mockito.mock(KeyEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_DISPATCH_SHORTCUT_EVENT])

        windowCallback.dispatchTouchEvent(Mockito.mock(MotionEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_DISPATCH_TOUCH_EVENT])

        windowCallback.dispatchTrackballEvent(Mockito.mock(MotionEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_DISPATCH_TRACK_BALL_EVENT])

        windowCallback.dispatchGenericMotionEvent(Mockito.mock(MotionEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_DISPATCH_MOTION_EVENT])

        windowCallback.dispatchPopulateAccessibilityEvent(Mockito.mock(AccessibilityEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_DISPATCH_POP_ACCESS])

        windowCallback.onCreatePanelView(1)
        Assert.assertEquals(true, methodCall[METHOD_ON_CREATE_PANEL_VIEW])

        windowCallback.onCreatePanelMenu(1, Mockito.mock(Menu::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_CREATE_PANEL_MENU])

        windowCallback.onPreparePanel(
            1,
            Mockito.mock(View::class.java),
            Mockito.mock(Menu::class.java)
        )
        Assert.assertEquals(true, methodCall[METHOD_ON_PREPARE_PANEL])

        windowCallback.onMenuOpened(1, Mockito.mock(Menu::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_MENU_OPEN])

        windowCallback.onMenuItemSelected(1, Mockito.mock(MenuItem::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_MENU_SEL])

        windowCallback.onWindowAttributesChanged(null)
        Assert.assertEquals(true, methodCall[METHOD_ON_WINDOW_ATT_CHANGED])

        windowCallback.onContentChanged()
        Assert.assertEquals(true, methodCall[METHOD_ON_CONTENT_CHANGED])

        windowCallback.onWindowFocusChanged(true)
        Assert.assertEquals(true, methodCall[METHOD_ON_WINDOW_FOCUS_CHANGED])

        windowCallback.onAttachedToWindow()
        Assert.assertEquals(true, methodCall[METHOD_ON_ATTACHED_TO_WINDOW])

        windowCallback.onDetachedFromWindow()
        Assert.assertEquals(true, methodCall[METHOD_ON_DETACHED_FROM_WINDOW])

        windowCallback.onPanelClosed(1, Mockito.mock(Menu::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_PANEL_CLOSED])

        windowCallback.onSearchRequested()
        Assert.assertEquals(true, methodCall[METHOD_ON_SEARCH_REQUEST])

        windowCallback.onSearchRequested(Mockito.mock(SearchEvent::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_SEARCH_REQUEST1])

        windowCallback.onWindowStartingActionMode(Mockito.mock(ActionMode.Callback::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_WINDOW_START_ACTION_MODE])

        windowCallback.onWindowStartingActionMode(Mockito.mock(ActionMode.Callback::class.java), 1)
        Assert.assertEquals(true, methodCall[METHOD_ON_WINDOW_START_ACTION_MODE1])

        windowCallback.onActionModeStarted(Mockito.mock(ActionMode::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_ACTION_MODE_STARTED])

        windowCallback.onActionModeFinished(Mockito.mock(ActionMode::class.java))
        Assert.assertEquals(true, methodCall[METHOD_ON_ACTION_MODE_FINISHED])
    }
}