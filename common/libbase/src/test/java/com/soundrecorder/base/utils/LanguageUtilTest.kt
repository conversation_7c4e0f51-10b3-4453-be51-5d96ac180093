/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: LanguageUtilTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.base.utils

import android.os.Build
import android.text.TextUtils
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.robolectric.annotation.Config
import java.util.Locale

@RunWith(AndroidJUnit4::class)
@PrepareForTest(Locale::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class LanguageUtilTest {

    @Test
    fun should_empty_when_getCurrentLanguageFromSystem() {
        val language = LanguageUtil.getCurrentLanguageFromSystem()
        Assert.assertTrue(!TextUtils.isEmpty(language))
    }

    @Test
    fun should_empty_when_isZHCN() {
        val isChina = LanguageUtil.isZHCN()
        Assert.assertFalse(isChina)
    }

    @Test
    fun should_empty_when_isZHTW() {
        val isZHTW = LanguageUtil.isZHTW()
        Assert.assertFalse(isZHTW)
    }

    @Test
    fun should_empty_when_isZHHK() {
        val isZHHK = LanguageUtil.isZHHK()
        Assert.assertFalse(isZHHK)
    }

    @Test
    fun should_empty_when_isUG() {
        val isUG = LanguageUtil.isUG()
        Assert.assertFalse(isUG)
    }

    @Test
    fun should_empty_when_isBO() {
        val isUG = LanguageUtil.isBO()
        Assert.assertFalse(isUG)
    }
}