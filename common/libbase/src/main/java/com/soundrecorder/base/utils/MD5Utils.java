/*******************************************************
 * Copyright 2010 - 2015 OPPO Mobile Comm Corp., Ltd. All rights reserved.
 *
 * Description : History : (ID, Date, Author, Description)
 *
 * 2015-08-06 Liukun Build.
 *******************************************************/
package com.soundrecorder.base.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import com.soundrecorder.base.BaseApplication;

@SuppressLint("UnsafeHashAlgorithmDetector")
public class MD5Utils {
    private static final String TAG = "MD5Utils";
    private static final int BYTE_BUFFER_LENGTH = 1024;

    public static String calcMd5(String text) {
        return calcMd5(text, "UTF-8");
    }

    private static String calcMd5(String text, String encoding) {
        if (text == null) {
            return null;
        } else {
            if (encoding == null) {
                encoding = "UTF-8";
            }
            try {
                return calcMd5(text.getBytes(encoding));
            } catch (UnsupportedEncodingException e) {
                DebugUtil.e(TAG, "calcMd5 first with an exception", e);
            }
            return null;
        }
    }

    private static String calcMd5(byte[] content) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(content);
            return convertToHexString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            DebugUtil.e(TAG, "calcMd5 second with an exception", e);
            return String.valueOf(new String(content, Charset.forName("UTF-8")).hashCode());
        }
    }

    public static String getMD5(File file) {
        InputStream inputStream = null;
        MessageDigest messageDigest = null;
        try {
            inputStream = new FileInputStream(file);
            messageDigest = MessageDigest.getInstance("MD5");

            byte[] buffer = new byte[BYTE_BUFFER_LENGTH];
            int numRead = inputStream.read(buffer, 0, BYTE_BUFFER_LENGTH);
            while (numRead > 0) {
                messageDigest.update(buffer, 0, numRead);
                numRead = inputStream.read(buffer, 0, BYTE_BUFFER_LENGTH);
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "getMD5 from file with an exception", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "inputStream close with an exception", e);
                }
            }
        }

        if (messageDigest != null) {
            return convertToHexString(messageDigest.digest());
        } else {
            return "";
        }
    }


    public static String getMD5(Uri mediaUri) {
        InputStream inputStream = null;
        MessageDigest messageDigest = null;
        Context context = BaseApplication.getAppContext();
        try {
            inputStream = context.getContentResolver().openInputStream(mediaUri);
            messageDigest = MessageDigest.getInstance("MD5");

            byte[] buffer = new byte[BYTE_BUFFER_LENGTH];
            if (inputStream != null) {
                int numRead = inputStream.read(buffer, 0, BYTE_BUFFER_LENGTH);
                while (numRead > 0) {
                    messageDigest.update(buffer, 0, numRead);
                    numRead = inputStream.read(buffer, 0, BYTE_BUFFER_LENGTH);
                }
            }
        } catch (Throwable e) {
            DebugUtil.e("TAG", "getMD5 from uri with an exception", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "inputStream uri close with an exception", e);
                }
            }
        }
        if (messageDigest != null) {
            return convertToHexString(messageDigest.digest());
        } else {
            return "";
        }
    }

    private static String convertToHexString(byte data[]) {
        int i = 0;
        StringBuffer buf = new StringBuffer("");
        for (int offset = 0; offset < data.length; offset++) {
            i = data[offset];
            if (i < 0) {
                i += 0x100;
            }
            if (i < 0x10) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(i));
        }

        return buf.toString();
    }
}
