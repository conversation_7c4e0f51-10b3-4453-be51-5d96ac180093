package com.oplus.recorderlog

import android.content.Context
import com.oplus.recorderlog.log.ILogProcess
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig
import com.soundrecorder.modulerouter.xlog.XLogAction

class XLogApiWrapper : ILogProcess {

    override fun v(tag: String?, message: String?) {
        XLogAction.v(tag, message)
    }

    override fun d(tag: String?, message: String?) {
        XLogAction.d(tag, message)
    }

    override fun i(tag: String?, message: String?) {
        XLogAction.i(tag, message)
    }

    override fun w(tag: String?, message: String?) {
        XLogAction.w(tag, message)
    }

    override fun e(tag: String?, message: String?) {
        XLogAction.e(tag, message)
    }

    override fun e(tag: String?, message: String?, e: Throwable?) {
        XLogAction.eWithThrowable(tag, message, e)
    }

    override fun initLog(context: Context) {
        XLogAction.initLog(context)
    }

    override fun flushLog(isSync: Boolean) {
        XLogAction.flushLog(isSync)
    }

    override fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        XLogAction.processPushLog(context, cloudLogConfigMsg)
    }

    override fun processManualReportLog() {
        XLogAction.processManualReportLog()
    }

    override fun processDBPrint(context: Context?) {
        XLogAction.processDBPrint(context)
    }
}