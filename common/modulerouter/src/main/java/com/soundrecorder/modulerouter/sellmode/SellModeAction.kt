/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SellModeAction
 Description:
 Version: 1.0
 Date: 2022/12/13
 Author: W9013333(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/12/13 1.0 create
 */

package com.soundrecorder.modulerouter.sellmode

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object SellModeAction {
    const val COMPONENT_NAME = "SellMode"
    const val CHECK_AND_START_SELL_MODE_SERVICE = "checkAndStartSellModeService"
    const val SELL_MODE_SCREEN_STATE_LISTENER = "createSellModeScreenStateListener"

    private val hasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun checkAndStartSellModeService(context: Context) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CHECK_AND_START_SELL_MODE_SERVICE).param(context).build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    @JvmStatic
    fun createSellModeScreenStateListener(lifecycleOwner: LifecycleOwner, screenChangedCheck: (() -> Boolean)?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SELL_MODE_SCREEN_STATE_LISTENER)
                .param(lifecycleOwner, screenChangedCheck).build()
            OStitch.execute<Void>(apiRequest)
        }
    }
}