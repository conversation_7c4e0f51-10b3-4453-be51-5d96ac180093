package com.recorder.cloudkit.tipstatus.dialog;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.recorder.cloudkit.R;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.shadows.ShadowStorageUtil;
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode;
import com.soundrecorder.base.BaseApplication;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

import kotlin.Pair;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowStorageUtil.class, ShadowFeatureOption.class})
public class CloudSyncStatusDialogHelperTest {

    private Context mContext;
    private MockedStatic<BaseApplication> mMockApplication;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mMockApplication = Mockito.mockStatic(BaseApplication.class);
        mMockApplication.when(() -> BaseApplication.getAppContext()) .thenReturn(mContext);
    }

    @After
    public void release() {
        mMockApplication.close();
        mMockApplication = null;
    }

    @Test
    public void should_returnExpectInt_when_whenStateChangeReturnRes() {
        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_NETWORK_NO_CONNECT, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.network_error_not_sync));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_NETWORK_TYPE_MISMATCH, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.network_error_not_sync));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_DATA_COLD_STANDBY, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.data_archived));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.local_storage_full_sync_pause));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_POWER_SAVING_MODE, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.open_save_power_mode_sync_pause));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_LOW_BATTERY, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.lower_power));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_LOW_BATTERY_CHARGING, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.lower_power_unchar));
            return null;
        });

        CloudSyncStatusDialogHelper.INSTANCE.whenStateChangeReturnRes(mContext, SyncErrorCode.RESULT_REQUEST_TOO_FREQUENT, (integer, integer2, integer3, s) -> {
            String text = mContext.getResources().getString(integer2);
            Assert.assertEquals(text, mContext.getResources().getString(R.string.server_sync_peak_period));
            return null;
        });
    }

    @Test
    public void should_returnExpectInt_when_getBatteryLevelAndChargingStatus() {
        Pair<Integer, Boolean> pair = CloudSyncStatusDialogHelper.INSTANCE.getBatteryLevelAndChargingStatus(mContext);
        Assert.assertNotNull(pair);
    }

    @Test
    public void should_returnExpectInt_when_startToCloudApp() {
        Boolean success = CloudSyncStatusDialogHelper.INSTANCE.startToCloudApp(mContext);
        Assert.assertEquals(false, success);
    }

   /* @Test
    public void should_returnExpectInt_when_startToSavePower() {
        Boolean success = CloudSyncStatusDialogHelper.INSTANCE.startToSavePower(mContext);
        Assert.assertEquals(success,true);
    }

    @Test
    public void should_returnExpectInt_when_startToWIFISetting() {
        Boolean success = CloudSyncStatusDialogHelper.INSTANCE.startToWIFISetting(mContext);
        Assert.assertEquals(success,true);
    }

    @Test
    public void should_returnExpectInt_when_startToClearData() {
        Boolean success = CloudSyncStatusDialogHelper.INSTANCE.startToClearData(mContext);
        Assert.assertEquals(success,true);
    }*/
}
