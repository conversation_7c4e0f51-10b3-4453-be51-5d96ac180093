package com.recorder.cloudkit.api

import android.content.Context
import androidx.lifecycle.LiveData
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.recorder.cloudkit.oldcompat.CloudPermissionActivity
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus

@Component(CloudTipManagerAction.COMPONENT_NAME)
object CloudTipManagerApi {
    private const val TAG = "CloudTipManagerApi"

    /**
     * 初始化云同步相关初始值，如：登录状态、开关状态
     */
    @Action(CloudTipManagerAction.ACTION_INIT_CLOUD_STATE)
    @JvmStatic
    fun initCloudState() {
        TipStatusManager.init()
    }

    /**
     * 注册云同步开关变化通知
     */
    @Action(CloudTipManagerAction.ACTION_REGISTER_CLOUD_SWITCH_LISTENER)
    @JvmStatic
    fun registerCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?) {
        DebugUtil.i(TAG, "registerCloudSwitchChangeListener")
        TipStatusManager.registerCloudListener(listener)
    }

    /**
     * 反注册云同步开关变化通知
     */
    @Action(CloudTipManagerAction.ACTION_UNREGISTER_CLOUD_SWITCH_LISTENER)
    @JvmStatic
    fun unregisterCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?) {
        TipStatusManager.unregisterCloudToggleListener(listener)
    }

    /**
     * 获取云同步状态
     */
    @Action(CloudTipManagerAction.ACTION_GET_CLOUD_SYNC_STATUS)
    @JvmStatic
    fun getCloudStatusLiveData(): LiveData<ITipStatus> {
        DebugUtil.i(TAG, "getCloudStatusLiveData")
        return TipStatusManager.tipStatusLiveData
    }

    /**
     * 获取云同步结果码
     */
    @Action(CloudTipManagerAction.ACTION_GET_CLOUD_SYNC_RESULT)
    @JvmStatic
    fun getCloudSyncResultCode(): Int {
        DebugUtil.i(TAG, "getCloudSyncResultCode")
        return TipStatusManager.syncResult
    }

    /**
     * 云同步开关装填
     * true：开启
     * false：关闭
     */
    @Action(CloudTipManagerAction.ACTION_GET_CLOUD_SWITCH_STATE_ON)
    @JvmStatic
    fun isCloudSwitchOn(): Boolean {
        return TipStatusManager.isCloudOn()
    }

    /**
     * 打开云同步开关设置页面
     */
    @Action(CloudTipManagerAction.ACTION_LAUNCH_CLOUD_SETTING_ACTIVITY)
    @JvmStatic
    fun launchCloudSettingPage(context: Context?,) {
        DebugUtil.i(TAG, "launchCloudSettingPage")
        BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_CLOUD, RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT)
        TipStatusManager.toCloudSetting(context)
    }

    /**
     * 打开云同步开关设置页面
     */
    @Action(CloudTipManagerAction.ACTION_GET_RECORD_SETTING_ACTIVITY_NAME)
    @JvmStatic
    fun getRecordCloudSettingActivityName(): String {
        return SettingRecordSyncActivity::class.java.name
    }

    @Action(CloudTipManagerAction.ACTION_CLOUD_PERMISSION_ACTIVITY_NAME)
    @JvmStatic
    fun getCloudPermissionActivityName(): String {
        return CloudPermissionActivity::class.java.name
    }

    @Action(CloudTipManagerAction.ACTION_CHECK_SYNC_ABNORMAL_STOP)
    @JvmStatic
    fun checkSyncAbnormalStop(context: Context): Boolean {
        DebugUtil.i(TAG, "checkSyncAbnormalStop")
        var isTrig = false
        // 不满足媒体库全量对比条件，是否满足 主动触发一次同步要求
        TipStatusManager.apply {
            if (needSyncCount > 0) {
                if (syncResult in ABNORMAL_STOP_SYNC_CODES) {
                    DebugUtil.i(TAG, "trig sync cloud onResume sync result in abnormal stop sync reason.")
                    CloudSyncAction.trigCloudSync(context, CloudSyncAction.SYNC_TYPE_RECOVERY_START_APP)
                    isTrig = true
                }
            }
        }
        return isTrig
    }

    @Action(CloudTipManagerAction.ACTION_IS_NEED_SHOW_CLOUD_GUIDE)
    @JvmStatic
    fun isNeedShowCloudGuide(): Boolean {
        return TipStatusManager.isNeedShowGuide()
    }

    @Action(CloudTipManagerAction.ACTION_IS_REFRESH_UI_OF_SYNC)
    @JvmStatic
    fun isRefreshUIOfSync(): Boolean {
        return TipStatusManager.isRefreshUIOfSync()
    }

    @Action(CloudTipManagerAction.ACTION_IS_SYNCING)
    @JvmStatic
    fun isSyncing(): Boolean {
        return TipStatusManager.isSyncing()
    }

    @Action(CloudTipManagerAction.ACTION_CHECK_NEED_SYNC_FULL_RECOVERY)
    @JvmStatic
    fun checkNeedSyncFullRecovery(): Boolean {
        return TipStatusManager.checkNeedSyncFullRecovery()
    }

    @Action(CloudTipManagerAction.ACTION_CLOUD_IS_LOGIN_FROM_CACHE)
    @JvmStatic
    fun isLoginFromCache(): Boolean {
        return CloudSynStateHelper.isLoginFromCache()
    }

    @Action(CloudTipManagerAction.ACTION_CLOUD_ACCOUNT_REQ_LOGIN)
    @JvmStatic
    fun accountRequestLogin(context: Context, callback: ((Boolean) -> Unit)? = null) {
        return CloudSynStateHelper.accountRequestLogin(context, callback)
    }

    @Action(CloudTipManagerAction.ACTION_CLOUD_RELEASE_ACCOUNT_REQ_CALLBACK)
    @JvmStatic
    fun releaseAccountReqCallback() {
        CloudSynStateHelper.releaseAccountReqCallback()
    }
}