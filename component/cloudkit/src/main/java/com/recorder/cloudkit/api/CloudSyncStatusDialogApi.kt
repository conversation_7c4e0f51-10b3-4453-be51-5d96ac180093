package com.recorder.cloudkit.api

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.account.AccountBean
import com.recorder.cloudkit.account.AccountManager
import com.recorder.cloudkit.account.IAccountInfoCallback
import com.recorder.cloudkit.tipstatus.dialog.CloudSyncStatusDialog
import com.recorder.cloudkit.tipstatus.dialog.CloudSyncStatusDialogHelper
import com.recorder.cloudkit.tipstatus.dialog.CloudUpgradeHelper
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.dialog.CloudSyncStatusDialogAction
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudSyncStatusDialog
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudUpgradeHelper

@Component(CloudSyncStatusDialogAction.COMPONENT_NAME)
object CloudSyncStatusDialogApi {
    private const val TAG = "CloudSyncStatusDialogApi"

    @JvmStatic
    @Action(CloudSyncStatusDialogAction.ACTION_NEW_STATUS_DIALOG)
    fun newCloudStatusDialog(mContext: Context, owner: LifecycleOwner?): ICloudSyncStatusDialog? {
        return CloudSyncStatusDialog(mContext, owner)
    }

    @JvmStatic
    @Action(CloudSyncStatusDialogAction.ACTION_NEW_CLOUD_UPGRADE_HELPER)
    fun newCloudUpgradeHelper(): ICloudUpgradeHelper? {
        return CloudUpgradeHelper()
    }

    /**
     * 请求登录
     */
    @JvmStatic
    @Action(CloudSyncStatusDialogAction.ACTION_REQUEST_LOGIN)
    fun requestSignLogin(context: Context) {
        AccountManager.sAccountManager.apply {
            reqSignInAccount(context, object : IAccountInfoCallback {
                /*这个account不靠谱，可能是null*/
                override fun onComplete(account: AccountBean) {
                    DebugUtil.i(TAG, "reqSignInAccount  $account")
                    val accountId = getLoginIdFromCache(BaseApplication.getAppContext())
                    if (accountId.isNullOrBlank().not()) {
                        // 登录成功，不同的账号，需要先执行退出登录逻辑，再执行同步逻辑
                        if (checkUserIdChanged(context, accountId)) {
                            saveUserId(context, accountId)
                            CloudSyncAction.stopSyncForLoginOut(BaseApplication.getAppContext(), false)
                            CloudSyncAction.trigCloudSync(BaseApplication.getAppContext(), CloudSyncAction.SYNC_TYPE_RECOVERY_MANUAL)
                        } else {
                            CloudSyncAction.trigCloudSync(BaseApplication.getAppContext(), CloudSyncAction.SYNC_TYPE_RECOVERY_START_APP)
                        }
                    }
                }
            })
        }
    }

    /**
     * 打开云服务快应用或浏览器H5
     */
    @JvmStatic
    @Action(CloudSyncStatusDialogAction.ACTION_LAUNCH_CLOUD_APP)
    fun launchCloudApp(context: Context) {
        CloudSyncStatusDialogHelper.startToCloudApp(context)
    }
}