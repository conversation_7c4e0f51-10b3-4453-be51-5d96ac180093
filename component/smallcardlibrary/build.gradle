plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: "../../common_build.gradle"

android {
    namespace 'com.oplus.soundrecorder.breenocardlibrary'

    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion

    defaultConfig {
        minSdkVersion 26
        targetSdkVersion prop_targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        encoding 'UTF-8'
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }

    //版本号由模块版本号和后缀组成，固定配置
    version = "${prop_smallCardVersionName}" + "${versionSuffix}"
    apply plugin: 'obuildplugin'
    OBuildConfig {
        //可选，如果是独立的SDK模块，可以配置这个参数，配置后编译task必须为  publishAllPublicationsToReleaseRepository
        //standAloneSdk = true
        //debug = true
        groupId = "${prop_archivesGroupName}"
        //配置SDK的artifactId，如果不配置，会使用当前module的目录名为sdkArtifactId
        sdkArtifactId = "smallCardLib"
        //执行SDK打包任务，约定为assembleReleaseSplitPixel，执行后，会将SDK推送到SNAPSHOT-maven：并同时发布到JFrog，以便后续正式提测发布；
        //内置assembleOapm，assembleRelease都可以出包；如果配置了 standAloneSdk = true，则task需要变为 publishAllPublicationsToReleaseRepository
//        sdkExecuteTask = "assembleReleaseSplitPixel"
        sdkExecuteTask = "assemblePublishToReleaseRepository"
        moduleDescription = "smallCard lib sdk, only use for small card(2x2)."
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.5.0'
    implementation 'com.google.android.material:material:1.6.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation "com.oplus.smartengine:customlib:1.0.5"
    implementation "com.oplus.smartengine:customannotation:1.0.2"
    //如果依赖其他组件，使用compileOnly，避免传递依赖，导致版本不一致
    implementation "com.google.code.gson:gson:$gson_version"
    implementation "com.airbnb.android:lottie:6.3.0"
    implementation "com.oplus.appcompat:core:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
}
