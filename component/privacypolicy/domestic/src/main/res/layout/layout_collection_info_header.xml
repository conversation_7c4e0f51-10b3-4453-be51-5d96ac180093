<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="@dimen/dp16"
    android:paddingEnd="@dimen/dp16"
    android:layout_marginStart="@dimen/dp16"
    android:layout_marginEnd="@dimen/dp16">
    <TextView
        android:id="@+id/tv_day_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textDirection="locale"
        android:text="@string/collection_info_instruction"
        style="@style/TextAppearance.COUI.Preference.Summary"
        android:textSize="@dimen/sp12"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_rotate_container"
        app:layout_constraintBottom_toBottomOf="@id/cl_rotate_container"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_rotate_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
         >
        <TextView
            android:id="@+id/tv_recently_menu_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            app:layout_constraintTop_toTopOf="@+id/recently_rotate_view"
            app:layout_constraintBottom_toBottomOf="@+id/recently_rotate_view"
            app:layout_constraintEnd_toStartOf="@+id/recently_rotate_view"
            android:layout_marginEnd="@dimen/dp4"
            style="@style/COUIPreferenceTitleStyle"
            android:textSize="12sp"
            android:textFontWeight="500"
            android:text="@string/collection_info_content_time_day7"
            app:layout_constraintHorizontal_bias="0"/>

        <com.coui.appcompat.rotateview.COUIRotateView
            android:id="@+id/recently_rotate_view"
            android:layout_width="@dimen/dp14"
            android:layout_height="@dimen/dp14"
            android:clickable="false"
            android:focusable="false"
            app:supportRotateType="couiRotateAloneZ"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/change_collect_expander_close_default"
            android:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>