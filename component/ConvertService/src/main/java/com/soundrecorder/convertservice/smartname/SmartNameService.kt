/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameService
 * * Description: SmartNameService
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: ********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  ********    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import android.app.Service
import android.content.Intent
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.convertservice.convert.IJobManagerLifeCycleCallback
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback

class SmartNameService : ISmartNameProcess, Service() {

    companion object {
        private const val TAG = "SmartNameService"
    }

    private var mMainHandler: Handler? = Handler(Looper.getMainLooper())

    var callback: IJobManagerLifeCycleCallback? = object : IJobManagerLifeCycleCallback {
        override fun onFinalJobEnd(mediaId: Long) {
            DebugUtil.i(TAG, "onFinalJobEnd, stop Service")
            stopSelf()
        }
    }

    override fun onCreate() {
        super.onCreate()
        SmartNameTaskManager.jobManagerLifeCycleCallback = callback
    }

    override fun onBind(intent: Intent?): IBinder {
        return SmartNameServiceBinder(this)
    }

    override fun onUnbind(intent: Intent?): Boolean {
        DebugUtil.i(TAG, "onUnbind")
        if (SmartNameTaskManager.checkNoTaskRunning()) {
            DebugUtil.i(TAG, "no TASK running , stop Service")
            stopSelf()
        }
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.d(TAG, "onDestroy")
        SmartNameTaskManager.releaseAll()
    }

    override fun initUnifiedSummary(callback: InitSummaryKitCallback?) {
        return SmartNameTaskManager.initUnifiedSummary(callback)
    }

    override fun startSmartName(mediaId: Long, params: SmartNameParam?): Boolean {
        return SmartNameTaskManager.startSmartName(mediaId, params)
    }

    override fun cancelSmartName(mediaId: Long): Boolean {
        return SmartNameTaskManager.cancelSmartName(mediaId)
    }

    override fun releaseSmartName(mediaId: Long) {
        return SmartNameTaskManager.releaseSmartName(mediaId)
    }

    override fun registerSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        return SmartNameTaskManager.registerSmartNameCallback(mediaId, callback)
    }

    override fun unregisterSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        return SmartNameTaskManager.unregisterSmartNameCallback(mediaId, callback)
    }
}