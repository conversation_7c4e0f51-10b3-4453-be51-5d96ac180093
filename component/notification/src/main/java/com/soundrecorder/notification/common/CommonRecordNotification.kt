/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonRecordNotification
 * * Description :  录制页锁屏标记notification
 * * Version     : 1.0
 * * Date        : 2022/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.app.Notification
import android.app.PendingIntent
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.widget.RemoteViews
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.modulerouter.miniapp.MiniRecorderAction
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecordAction
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.notification.R

class CommonRecordNotification(groupId: Int, notificationId: Int) :
    CommonNotification(groupId, notificationId) {

    companion object {
        private const val CARD_SERVICE_ID = "op_fluid_serviceId"
    }

    override var logTag: String
        get() = "CommonRecordNotification"
        set(value) {}

    override fun getOldChannelId(): String {
        return NotificationUtils.RECORDERSERVICE_OLD_CID
    }

    override fun getChannelId(): String {
        return NotificationUtils.RECORDERSERVICE_CID
    }

    override fun getChannelName(): String {
        return defaultContext.resources.getString(R.string.recording_channel_name)
    }

    @Suppress("UnsafeCallOnNullableType")
    override fun showImmediately() {
        if (service != null && notification != null) {
            runCatching {
                service!!.startForeground(
                    getGroupId(),
                    notification!!,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
                )
            }.onFailure {
                DebugUtil.e(logTag, "showImmediately startForeground error $it")
            }
        }
    }

    override fun initNotification() {
        super.initNotification()
        val id = SeedlingAction.getCardServiceId()
        id?.let {
            notification?.extras?.putString(CARD_SERVICE_ID, it)
        }
    }

    override fun getLayoutId(): Int {
        return if (BaseUtil.isAndroidSOrLater) {
            R.layout.layout_notification_play_back
        } else {
            R.layout.layout_notification_play_back_below_12
        }
    }

    override fun getFoldLayoutId(): Int? {
        return if (BaseUtil.isAndroidSOrLater) {
            R.layout.layout_notification_play_back_fold
        } else {
            null
        }
    }

    override fun setRemoteViewData(remoteViews: RemoteViews) {
        super.setRemoteViewData(remoteViews)
        //最近标记文本信息
        setMarkContent()
        //标记按钮状态信息
        setMarkBtnStatus()
        if (BaseUtil.isAndroidSOrLater) {
            // 保存按钮信息
            setSaveBtnStatus()
            val isSavingState = isSaving()
            setViewVisibility(R.id.mark_btn_area, !isSavingState)
            setViewVisibility(R.id.play_btn_area, !isSavingState)
            setViewVisibility(R.id.save_btn_area, !isSavingState)
        }
    }

    override fun isMarkButtonEnable(): Boolean = (notificationModel?.isMarkEnabled?.value != false)

    /**
     * S版本及以上：标题显示录制时间，文本显示“正在录音...”
     * 以下：标题显示录音名称，文本显示录制时间
     */
    override fun getContentTitle(): Pair<String, String> {
        return if (BaseUtil.isAndroidSOrLater) {
            getTimeText()
        } else {
            getRecordStatusStr()
        }
    }


    /**
     * S版本及以上：标题显示录制时间，文本显示“正在录音...”
     * 以下：标题显示录音名称，文本显示录制时间
     */
    override fun getContentText(): Pair<String, String> {
        return if (BaseUtil.isAndroidSOrLater) {
            getRecordStatusStr()
        } else {
            getTimeText()
        }
    }

    private fun getTimeText(): Pair<String, String> {
        val time = notificationModel?.curTime?.value ?: 0
        return Pair(
            TimeUtils.getFormatTimeExclusiveMill(time),
            TimeUtils.getDurationHint(defaultContext, time)
        )
    }

    private fun getRecordStatusStr(): Pair<String, String> {
        return CommonNotificationResourceHelper.getRecordStatusContent(defaultContext, isPlaying(), isSaving())
    }

    private fun isSaving(): Boolean {
        val state = notificationModel?.saveState?.value
        return state != null && state != RecorderViewModelAction.SaveFileState.INIT
    }

    override fun setPlayBtnStatus() {
        val isPlayingState = isPlaying()
        if (BaseUtil.isAndroidSOrLater) {
            remoteViews.setTextViewText(
                R.id.play_btn,
                CommonNotificationResourceHelper.getPlayButtonRecordText(
                    defaultContext,
                    isPlayingState
                )
            )
        } else {
            remoteViews.setImageViewResource(
                R.id.play_btn,
                CommonNotificationResourceHelper.getPlayButtonImageResId(isPlayingState)
            )
            remoteViews.setContentDescription(
                R.id.play_btn,
                CommonNotificationResourceHelper.getPlayButtonRecordContentDec(
                    defaultContext,
                    isPlayingState
                )
            )
        }
    }

    override fun getPlayButtonAction(): Notification.Action? {
        if (isSaving()) {
            return null
        }
        val isPlayingState = isPlaying()
        return Notification.Action.Builder(
//            Icon.createWithResource(defaultContext, CommonNotificationResourceHelper.getPlayButtonImageResId(isPlayingState)),
            null,
            CommonNotificationResourceHelper.getPlayButtonRecordText(defaultContext, isPlayingState),
            getPlayButtonPendingIntent()).build()
    }

    override fun getMarkButtonAction(): Notification.Action? {
        if (isSaving()) {
            return null
        }
        return Notification.Action.Builder(
//            Icon.createWithResource(defaultContext, CommonNotificationResourceHelper.getMarkButtonResId()),
            null,
            defaultContext.getString(R.string.talkback_flag),
            getMarkButtonPendingIntent()).build()
    }

    override fun getSaveButtonAction(): Notification.Action? {
        if (isSaving()) {
            return null
        }
        return Notification.Action.Builder(
            null,
            defaultContext.getString(R.string.rename_save),
            getSaveButtonPendingIntent()).build()
    }

    override fun isBtnEnabled(): Boolean {
        return super.isBtnEnabled() && ((notificationModel?.curTime?.value ?: 0) > 0)
    }

    override fun getJumpIntent(): Intent? {
        return RecordAction.getNotificationIntent(defaultContext)?.also {
            it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
    }

    override fun getNotificationBuilder(): Notification.Builder {
        val builder = super.getNotificationBuilder()
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            builder.setForegroundServiceBehavior(Notification.FOREGROUND_SERVICE_IMMEDIATE)
        } else builder
    }

    override fun getOtherDisplayContentIntent(): PendingIntent {
        val miniAppIntent = MiniRecorderAction.createMiniAppIntent(defaultContext)
        val miniAppPendingIntent = PendingIntent.getActivity(defaultContext, PENDING_INTENT_REQUEST_CODE, miniAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        // 添加外屏跳转minApp跳转intent到Extra中
        val intent = RecordAction.createRecorderIntent(defaultContext, true)?.also {
            it.putExtra(OPEN_MINI_RECORDER, miniAppPendingIntent)
        }

        return PendingIntent.getActivity(defaultContext, PENDING_INTENT_REQUEST_CODE, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
    }
}