/********************************************************************************
 ** Copyright (C), 2008-2013, OPPO Mobile Comm Corp., Ltd
 ** VENDOR_EDIT, All rights reserved.
 **
 ** File: - OppoRecorder.java
 ** Description:
 **     the recorder interface
 **
 ** Version: 1.0
 ** Date: 2012-12-10
 ** Author: <EMAIL>
 ** OPPO Coding Static Checking Skip
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <data>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>      2012-12-10   1.0         Create this moudle
 ********************************************************************************/

package com.oppo.media;

import android.hardware.Camera;
import android.media.CamcorderProfile;
import android.view.Surface;

import java.io.FileDescriptor;
import java.io.IOException;

/**
 * Used to record audio and video. The functions are the same with the android mediarecorder class
 * except the OPPO's function: pause, resume, expandFile.
 */

//copy public interfaces from vendor\oppo_media\base\java\com\oppo\media
//just for compile ok!
public class OppoRecorder {
    /*OPPO 2011-08-13  Maojg Add begin for add for record quality*/
    public static final int NAMR_BITRATE = 5150;
    public static final int HAMR_BITRATE = 12200;
    public static final int NWAV_SAMPLERATE = 8000;
    public static final int HWAV_SAMPLERATE = 44100;
    public static final int NAMR_BYTES_P_SEC = 700;
    public static final int HAMR_BYTES_P_SEC = 1600;
    public static final int NWAV_BYTES_P_SEC = 16000;
    public static final int HWAV_BYTES_P_SEC = 88200;

    public OppoRecorder() {
    }

    /**
     * Sets a Camera to use for recording. Use this function to switch quickly between preview and
     * capture mode without a teardown of the camera object.
     * {@link Camera#unlock()} should be called before this. Must call before
     * prepare().
     *
     * @param c the Camera to use for recording
     */
    public native void setCamera(Camera c);

    /**
     * Sets a Surface to show a preview of recorded media (video). Calls this before prepare() to
     * make sure that the desirable preview display is set. If {@link #setCamera(Camera)} is used
     * and the surface has been already set to the camera, application do not need to call this. If
     * this is called with non-null surface, the preview surface of the camera will be replaced by
     * the new surface. If this method is called with null surface or not called at all, oppo
     * recorder will not change the preview surface of the camera.
     *
     * @param sv the Surface to use for the preview
     * @see Camera#setPreviewDisplay(android.view.SurfaceHolder)
     */
    public void setPreviewDisplay(Surface sv) {
    }

    /**
     * Defines the audio source. These constants are used with
     * {@link #setAudioSource(int)}.
     */
    public static final class AudioSource {
        /**
         * Default audio source
         **/
        public static final int DEFAULT = 0;

        /**
         * Microphone audio source
         */
        public static final int MIC = 1;

        /**
         * Voice call uplink (Tx) audio source
         */
        public static final int VOICE_UPLINK = 2;

        /**
         * Voice call downlink (Rx) audio source
         */
        public static final int VOICE_DOWNLINK = 3;

        /**
         * Voice call uplink + downlink audio source
         */
        public static final int VOICE_CALL = 4;

        /**
         * Microphone audio source with same orientation as camera if available, the main device
         * microphone otherwise
         */
        public static final int CAMCORDER = 5;

        /**
         * Microphone audio source tuned for voice recognition if available, behaves like
         * {@link #DEFAULT} otherwise.
         */
        public static final int VOICE_RECOGNITION = 6;

        /**
         * Microphone audio source tuned for voice communications such as VoIP. It will for instance
         * take advantage of echo cancellation or automatic gain control if available. It otherwise
         * behaves like {@link #DEFAULT} if no voice processing is applied.
         */
        public static final int VOICE_COMMUNICATION = 7;

        /* Do not change these values without updating their counterparts
         * in system/core/include/system/audio.h!
         */
        private AudioSource() { }
    }

    /**
     * Defines the video source. These constants are used with
     * {@link #setVideoSource(int)}.
     */
    public static final class VideoSource {
        public static final int DEFAULT = 0;
        /**
         * Camera video source
         */
        public static final int CAMERA = 1;
        /**
         * @hide
         */
        public static final int GRALLOC_BUFFER = 2;

        /* Do not change these values without updating their counterparts
         * in include/media/mediarecorder.h!
         */
        private VideoSource() { }
    }

    /**
     * Defines the output format. These constants are used with
     * {@link #setOutputFormat(int)}.
     */
    public static final class OutputFormat {
        public static final int DEFAULT = 0;
        /**
         * 3GPP media file format
         */
        public static final int THREE_GPP = 1;
        /**
         * MPEG4 media file format
         */
        public static final int MPEG_4 = 2;

        /** The following formats are audio only .aac or .amr formats */

        /**
         * AMR NB file format
         *
         * @deprecated Deprecated in favor of MediaRecorder.OutputFormat.AMR_NB
         */
        @Deprecated
        public static final int RAW_AMR = 3;

        /**
         * AMR NB file format
         */
        public static final int AMR_NB = 3;

        /**
         * AMR WB file format
         */
        public static final int AMR_WB = 4;

        /**
         * @hide AAC ADIF file format
         */
        public static final int AAC_ADIF = 5;

        /**
         * AAC ADTS file format
         */
        public static final int AAC_ADTS = 6;

        /**
         * @hide Stream over a socket, limited to a single stream
         */
        public static final int OUTPUT_FORMAT_RTP_AVP = 7;

        /**
         * @hide H.264/AAC data encapsulated in MPEG2/TS
         */
        public static final int OUTPUT_FORMAT_MPEG2TS = 8;

        public static final int MP3 = 9;
        /*OPPO Maojg add for:add wav format,2011-08-13*/
        public static final int WAV = 11;

        /* Do not change these values without updating their counterparts
         * in include/media/mediarecorder.h!
         */
        private OutputFormat() { }
    }

    /**
     * Defines the audio encoding. These constants are used with
     * {@link #setAudioEncoder(int)}.
     */
    public static final class AudioEncoder {

        public static final int DEFAULT = 0;
        /**
         * AMR (Narrowband) audio codec
         */
        public static final int AMR_NB = 1;
        /**
         * AMR (Wideband) audio codec
         */
        public static final int AMR_WB = 2;
        /**
         * AAC Low Complexity (AAC-LC) audio codec
         */
        public static final int AAC = 3;
        /**
         * High Efficiency AAC (HE-AAC) audio codec
         */
        public static final int HE_AAC = 4;
        /**
         * Enhanced Low Delay AAC (AAC-ELD) audio codec
         */
        public static final int AAC_ELD = 5;

        public static final int MPEG = 6;

        /*OPPO Maojg add for:add wav format,2011-08-13*/
        public static final int WAV = 8;

        /* Do not change these values without updating their counterparts
         * in include/media/mediarecorder.h!
         */
        private AudioEncoder() { }
    }

    /**
     * Defines the video encoding. These constants are used with
     * {@link #setVideoEncoder(int)}.
     */
    public static final class VideoEncoder {
        public static final int DEFAULT = 0;
        public static final int H263 = 1;
        public static final int H264 = 2;
        public static final int MPEG_4_SP = 3;

        /* Do not change these values without updating their counterparts
         * in include/media/mediarecorder.h!
         */
        private VideoEncoder() { }
    }

    /**
     * Sets the audio source to be used for recording. If this method is not called, the output file
     * will not contain an audio track. The source needs to be specified before setting
     * recording-parameters or encoders. Call this only before setOutputFormat().
     *
     * @param audioSource the audio source to use
     * @throws IllegalStateException if it is called after setOutputFormat()
     * @see android.media.MediaRecorder.AudioSource
     */
    public native void setAudioSource(int audioSource) throws IllegalStateException;

    /**
     * Gets the maximum value for audio sources.
     *
     * @see android.media.MediaRecorder.AudioSource
     */
    public static final int getAudioSourceMax() {
        return AudioSource.VOICE_COMMUNICATION;
    }

    /**
     * Sets the video source to be used for recording. If this method is not called, the output file
     * will not contain an video track. The source needs to be specified before setting
     * recording-parameters or encoders. Call this only before setOutputFormat().
     *
     * @param videoSource the video source to use
     * @throws IllegalStateException if it is called after setOutputFormat()
     * @see android.media.MediaRecorder.VideoSource
     */
    public native void setVideoSource(int videoSource) throws IllegalStateException;

    /**
     * Uses the settings from a CamcorderProfile object for recording. This method should be called
     * after the video AND audio sources are set, and before setOutputFile(). If a time lapse
     * CamcorderProfile is used, audio related source or recording parameters are ignored.
     *
     * @param profile the CamcorderProfile to use
     * @see CamcorderProfile
     */
    public void setProfile(CamcorderProfile profile) {
    }

    /**
     * Set video frame capture rate. This can be used to set a different video frame capture rate
     * than the recorded video's playback rate. This method also sets the recording mode to time
     * lapse. In time lapse video recording, only video is recorded. Audio related parameters are
     * ignored when a time lapse recording session starts, if an application sets them.
     *
     * @param fps Rate at which frames should be captured in frames per second. The fps can go as
     *            low as desired. However the fastest fps will be limited by the hardware. For
     *            resolutions that can be captured by the video camera, the fastest fps can be
     *            computed using
     *            {@link Camera.Parameters#getPreviewFpsRange(int[])}. For higher
     *            resolutions the fastest fps may be more restrictive. Note that the recorder cannot
     *            guarantee that frames will be captured at the given rate due to camera/encoder
     *            limitations. However it tries to be as close as possible.
     */
    public void setCaptureRate(double fps) {
    }

    /**
     * Sets the orientation hint for output video playback. This method should be called before
     * prepare(). This method will not trigger the source video frame to rotate during video
     * recording, but to add a composition matrix containing the rotation angle in the output video
     * if the output format is OutputFormat.THREE_GPP or OutputFormat.MPEG_4 so that a video player
     * can choose the proper orientation for playback. Note that some video players may choose to
     * ignore the compostion matrix in a video during playback.
     *
     * @param degrees the angle to be rotated clockwise in degrees. The supported angles are 0, 90,
     *                180, and 270 degrees.
     * @throws IllegalArgumentException if the angle is not supported.
     */
    public void setOrientationHint(int degrees) {
    }

    /**
     * Set and store the geodata (latitude and longitude) in the output file. This method should be
     * called before prepare(). The geodata is stored in udta box if the output format is
     * OutputFormat.THREE_GPP or OutputFormat.MPEG_4, and is ignored for other output formats. The
     * geodata is stored according to ISO-6709 standard.
     *
     * @param latitude  latitude in degrees. Its value must be in the range [-90, 90].
     * @param longitude longitude in degrees. Its value must be in the range [-180, 180].
     * @throws IllegalArgumentException if the given latitude or longitude is out of range.
     */
    public void setLocation(float latitude, float longitude) {
    }

    /**
     * Sets the format of the output file produced during recording. Call this after
     * setAudioSource()/setVideoSource() but before prepare().
     * <p>
     * <p>
     * It is recommended to always use 3GP format when using the H.263 video encoder and AMR audio
     * encoder. Using an MPEG-4 container format may confuse some desktop players.
     * </p>
     *
     * @param outputFormat the output format to use. The output format needs to be specified before
     *                      setting recording-parameters or encoders.
     * @throws IllegalStateException if it is called after prepare() or before
     *                               setAudioSource()/setVideoSource().
     * @see android.media.MediaRecorder.OutputFormat
     */
    public native void setOutputFormat(int outputFormat) throws IllegalStateException;

    /**
     * Sets the width and height of the video to be captured. Must be called after setVideoSource().
     * Call this after setOutFormat() but before prepare().
     *
     * @param width  the width of the video to be captured
     * @param height the height of the video to be captured
     * @throws IllegalStateException if it is called after prepare() or before setOutputFormat()
     */
    public native void setVideoSize(int width, int height) throws IllegalStateException;

    /**
     * Sets the frame rate of the video to be captured. Must be called after setVideoSource(). Call
     * this after setOutFormat() but before prepare().
     *
     * @param rate the number of frames per second of video to capture
     * @throws IllegalStateException if it is called after prepare() or before setOutputFormat().
     *                               <p>
     *                               NOTE: On some devices that have auto-frame rate, this sets the maximum frame
     *                               rate, not a constant frame rate. Actual frame rate will vary according to
     *                               lighting conditions.
     */
    public native void setVideoFrameRate(int rate) throws IllegalStateException;

    /**
     * Sets the maximum duration (in ms) of the recording session. Call this after setOutFormat()
     * but before prepare(). After recording reaches the specified duration, a notification will be
     * sent to the {@link android.media.MediaRecorder.OnInfoListener} with a "what" code of
     * {@link #MEDIA_RECORDER_INFO_MAX_DURATION_REACHED} and recording will be stopped. Stopping
     * happens asynchronously, there is no guarantee that the recorder will have stopped by the time
     * the listener is notified.
     *
     * @param maxDurationMs the maximum duration in ms (if zero or negative, disables the duration
     *                        limit)
     */
    public native void setMaxDuration(int maxDurationMs) throws IllegalArgumentException;

    /**
     * Sets the maximum filesize (in bytes) of the recording session. Call this after setOutFormat()
     * but before prepare(). After recording reaches the specified filesize, a notification will be
     * sent to the {@link android.media.MediaRecorder.OnInfoListener} with a "what" code of
     * {@link #MEDIA_RECORDER_INFO_MAX_FILESIZE_REACHED} and recording will be stopped. Stopping
     * happens asynchronously, there is no guarantee that the recorder will have stopped by the time
     * the listener is notified.
     *
     * @param maxFileSizeBytes the maximum filesize in bytes (if zero or negative, disables the
     *                           limit)
     */
    public native void setMaxFileSize(long maxFileSizeBytes) throws IllegalArgumentException;

    /**
     * Sets the audio encoder to be used for recording. If this method is not called, the output
     * file will not contain an audio track. Call this after setOutputFormat() but before prepare().
     *
     * @param audioEncoder the audio encoder to use.
     * @throws IllegalStateException if it is called before setOutputFormat() or after prepare().
     * @see android.media.MediaRecorder.AudioEncoder
     */
    public native void setAudioEncoder(int audioEncoder) throws IllegalStateException;

    /**
     * Sets the video encoder to be used for recording. If this method is not called, the output
     * file will not contain an video track. Call this after setOutputFormat() and before prepare().
     *
     * @param videoEncoder the video encoder to use.
     * @throws IllegalStateException if it is called before setOutputFormat() or after prepare()
     * @see android.media.MediaRecorder.VideoEncoder
     */
    public native void setVideoEncoder(int videoEncoder) throws IllegalStateException;

    /**
     * Sets the audio sampling rate for recording. Call this method before prepare(). Prepare() may
     * perform additional checks on the parameter to make sure whether the specified audio sampling
     * rate is applicable. The sampling rate really depends on the format for the audio recording,
     * as well as the capabilities of the platform. For instance, the sampling rate supported by AAC
     * audio coding standard ranges from 8 to 96 kHz, the sampling rate supported by AMRNB is 8kHz,
     * and the sampling rate supported by AMRWB is 16kHz. Please consult with the related audio
     * coding standard for the supported audio sampling rate.
     *
     * @param samplingRate the sampling rate for audio in samples per second.
     */
    public void setAudioSamplingRate(int samplingRate) {
    }

    /**
     * Sets the number of audio channels for recording. Call this method before prepare(). Prepare()
     * may perform additional checks on the parameter to make sure whether the specified number of
     * audio channels are applicable.
     *
     * @param numChannels the number of audio channels. Usually it is either 1 (mono) or 2 (stereo).
     */
    public void setAudioChannels(int numChannels) {
    }

    /**
     * Sets the audio encoding bit rate for recording. Call this method before prepare(). Prepare()
     * may perform additional checks on the parameter to make sure whether the specified bit rate is
     * applicable, and sometimes the passed bitRate will be clipped internally to ensure the audio
     * recording can proceed smoothly based on the capabilities of the platform.
     *
     * @param bitRate the audio encoding bit rate in bits per second.
     */
    public void setAudioEncodingBitRate(int bitRate) {
    }

    /**
     * Sets the video encoding bit rate for recording. Call this method before prepare(). Prepare()
     * may perform additional checks on the parameter to make sure whether the specified bit rate is
     * applicable, and sometimes the passed bitRate will be clipped internally to ensure the video
     * recording can proceed smoothly based on the capabilities of the platform.
     *
     * @param bitRate the video encoding bit rate in bits per second.
     */
    public void setVideoEncodingBitRate(int bitRate) {
    }

    /**
     * Currently not implemented. It does nothing.
     *
     * @hide
     * @deprecated Time lapse mode video recording using camera still image capture is not
     * desirable, and will not be supported.
     */
    @Deprecated
    public void setAuxiliaryOutputFile(FileDescriptor fd) {
    }

    /**
     * Currently not implemented. It does nothing.
     *
     * @hide
     * @deprecated Time lapse mode video recording using camera still image capture is not
     * desirable, and will not be supported.
     */
    @Deprecated
    public void setAuxiliaryOutputFile(String path) {
    }

    /**
     * Pass in the file descriptor of the file to be written. Call this after setOutputFormat() but
     * before prepare().
     *
     * @param fd an open file descriptor to be written into.
     * @throws IllegalStateException if it is called before setOutputFormat() or after prepare()
     */
    public void setOutputFile(FileDescriptor fd) throws IllegalStateException {
    }

    /**
     * Sets the path of the output file to be produced. Call this after setOutputFormat() but before
     * prepare().
     *
     * @param path The pathname to use.
     * @throws IllegalStateException if it is called before setOutputFormat() or after prepare()
     */
    public void setOutputFile(String path) throws IllegalStateException {
    }

    // native implementation

    /**
     * Prepares the recorder to begin capturing and encoding data. This method must be called after
     * setting up the desired audio and video sources, encoders, file format, etc., but before
     * start().
     *
     * @throws IllegalStateException if it is called after start() or before setOutputFormat().
     * @throws IOException           if prepare fails otherwise.
     */
    public void prepare() throws IllegalStateException, IOException {
    }

    /**
     * Begins capturing and encoding data to the file specified with setOutputFile(). Call this
     * after prepare().
     * <p>
     * <p>
     * Since API level 13, if applications set a camera via {@link #setCamera(Camera)}, the apps can
     * use the camera after this method call. The apps do not need to lock the camera again.
     * However, if this method fails, the apps should still lock the camera back. The apps should
     * not start another recording session during recording.
     *
     * @throws IllegalStateException if it is called before prepare().
     */
    public native void start() throws IllegalStateException;

    /**
     * Stops recording. Call this after start(). Once recording is stopped, you will have to
     * configure it again as if it has just been constructed. Note that a RuntimeException is
     * intentionally thrown to the application, if no valid audio/video data has been received when
     * stop() is called. This happens if stop() is called immediately after start(). The failure
     * lets the application take action accordingly to clean up the output file (delete the output
     * file, for instance), since the output file is not properly constructed when this happens.
     *
     * @throws IllegalStateException if it is called before start()
     */
    public native void stop() throws IllegalStateException;

    /**
     * Restarts the MediaRecorder to its idle state. After calling this method, you will have to
     * configure it again as if it had just been constructed.
     */
    public void reset() {
    }

    /**
     * Returns the maximum absolute amplitude that was sampled since the last call to this method.
     * Call this only after the setAudioSource().
     *
     * @return the maximum absolute amplitude measured since the last call, or 0 when called for the
     * first time
     * @throws IllegalStateException if it is called before the audio source has been set.
     */
    public native int getMaxAmplitude() throws IllegalStateException;

    /* Do not change this value without updating its counterpart
     * in include/media/mediarecorder.h!
     */
    /**
     * Unspecified media recorder error.
     *
     * @see android.media.MediaRecorder.OnErrorListener
     */
    public static final int MEDIA_RECORDER_ERROR_UNKNOWN = 1;

    /**
     * Interface definition for a callback to be invoked when an error occurs while recording.
     */
    public interface OnErrorListener {

        /**
         * Called when an error occurs while recording.
         *
         * @param mr    the MediaRecorder that encountered the error
         * @param what  the type of error that has occurred:
         *              <ul>
         *              <li>{@link #MEDIA_RECORDER_ERROR_UNKNOWN}
         *              </ul>
         * @param extra an extra code, specific to the error type
         */
        void onError(OppoRecorder mr, int what, int extra);
    }

    /**
     * Register a callback to be invoked when an error occurs while recording.
     *
     * @param l the callback that will be run
     */
    public void setOnErrorListener(OnErrorListener l) {
    }

    /* Do not change these values without updating their counterparts
     * in include/media/mediarecorder.h!
     */
    /**
     * Unspecified media recorder error.
     *
     * @see android.media.MediaRecorder.OnInfoListener
     */
    public static final int MEDIA_RECORDER_INFO_UNKNOWN = 1;
    /**
     * A maximum duration had been setup and has now been reached.
     *
     * @see android.media.MediaRecorder.OnInfoListener
     */
    public static final int MEDIA_RECORDER_INFO_MAX_DURATION_REACHED = 800;
    /**
     * A maximum filesize had been setup and has now been reached.
     *
     * @see android.media.MediaRecorder.OnInfoListener
     */
    public static final int MEDIA_RECORDER_INFO_MAX_FILESIZE_REACHED = 801;

    /**
     * informational events for individual tracks, for testing purpose. The track informational
     * event usually contains two parts in the ext1 arg of the onInfo() callback: bit 31-28 contains
     * the track id; and the rest of the 28 bits contains the informational event defined here. For
     * example, ext1 = (1 << 28 | MEDIA_RECORDER_TRACK_INFO_TYPE) if the track id is 1 for
     * informational event MEDIA_RECORDER_TRACK_INFO_TYPE; while ext1 = (0 << 28 |
     * MEDIA_RECORDER_TRACK_INFO_TYPE) if the track id is 0 for informational event
     * MEDIA_RECORDER_TRACK_INFO_TYPE. The application should extract the track id and the type of
     * informational event from ext1, accordingly.
     * <p>
     * FIXME: Please update the comment for onInfo also when these events are unhidden so that
     * application knows how to extract the track id and the informational event type from onInfo
     * callback.
     * <p>
     * {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_LIST_START = 1000;
    /**
     * Signal the completion of the track for the recording session. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_COMPLETION_STATUS = 1000;
    /**
     * Indicate the recording progress in time (ms) during recording. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_PROGRESS_IN_TIME = 1001;
    /**
     * Indicate the track type: 0 for Audio and 1 for Video. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_TYPE = 1002;
    /**
     * Provide the track duration information. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_DURATION_MS = 1003;
    /**
     * Provide the max chunk duration in time (ms) for the given track. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_MAX_CHUNK_DUR_MS = 1004;
    /**
     * Provide the total number of recordd frames. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_ENCODED_FRAMES = 1005;
    /**
     * Provide the max spacing between neighboring chunks for the given track. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INTER_CHUNK_TIME_MS = 1006;
    /**
     * Provide the elapsed time measuring from the start of the recording till the first output
     * frame of the given track is received, excluding any intentional start time offset of a
     * recording session for the purpose of eliminating the recording sound in the recorded file.
     * {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_INITIAL_DELAY_MS = 1007;
    /**
     * Provide the start time difference (delay) betweeen this track and the start of the movie.
     * {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_START_OFFSET_MS = 1008;
    /**
     * Provide the total number of data (in kilo-bytes) encoded. {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_DATA_KBYTES = 1009;
    /**
     * {@hide}
     */
    public static final int MEDIA_RECORDER_TRACK_INFO_LIST_END = 2000;

    /**
     * Interface definition for a callback to be invoked when an error occurs while recording.
     */
    public interface OnInfoListener {

        /**
         * Called when an error occurs while recording.
         *
         * @param mr    the MediaRecorder that encountered the error
         * @param what  the type of error that has occurred:
         *              <ul>
         *              <li>{@link #MEDIA_RECORDER_INFO_UNKNOWN}
         *              <li>{@link #MEDIA_RECORDER_INFO_MAX_DURATION_REACHED}
         *              <li>{@link #MEDIA_RECORDER_INFO_MAX_FILESIZE_REACHED}
         *              </ul>
         * @param extra an extra code, specific to the error type
         */
        void onInfo(OppoRecorder mr, int what, int extra);
    }

    /**
     * Register a callback to be invoked when an informational event occurs while recording.
     *
     * @param listener the callback that will be run
     */
    public void setOnInfoListener(OnInfoListener listener) {
    }

    /**
     * Called from native code when an interesting event happens. This method just uses the
     * EventHandler system to post the event back to the main app thread. We use a weak reference to
     * the original MediaRecorder object so that the native code is safe from the object
     * disappearing from underneath it. (This is the cookie passed to native_setup().)
     */
    private static void postEventFromNative(Object mediarecorderRef, int what, int arg1, int arg2,
                                            Object obj) {
    }

    /**
     * Releases resources associated with this MediaRecorder object. It is good practice to call
     * this method when you're done using the MediaRecorder. In particular, whenever an Activity of
     * an application is paused (its onPause() method is called), or stopped (its onStop() method is
     * called), this method should be invoked to release the MediaRecorder object, unless the
     * application has a special need to keep the object around. In addition to unnecessary
     * resources (such as memory and instances of codecs) being held, failure to call this method
     * immediately if a MediaRecorder object is no longer needed may also lead to continuous battery
     * consumption for mobile devices, and recording failure for other applications if no multiple
     * instances of the same codec are supported on a device. Even if multiple instances of the same
     * codec are supported, some performance degradation may be expected when unnecessary multiple
     * instances are used at the same time.
     */
    public native void release();

    private native void setParameter(String nameValuePair);


    /*add begin by Maojg 2011-08-13*/
    public native void pause() throws IllegalStateException;

    /**
     * pause recording. call this when recording has started
     *
     * @throws IllegalStateException
     */
    public native void resume() throws IllegalStateException;

    /**
     * resume recording. call this when recording has been paused
     *
     * @throws IllegalStateException
     */
    /*add end by maojg 2011-08-13*/

    /******************************************************************************
     * OPPO maojg add 2011-08-13 Description : expand the record file Parameter: <IN> String
     * path:the expand file name <IN> int as: audio source Returns: ps: It's just for "amr_nb" and
     * "wav".
     ******************************************************************************/
    public native void expandFile(String path, int as) throws IllegalStateException;

    /*add end by maojg 2011-08-13*/

    //support android q
    public native void expandFile(FileDescriptor fd, long offset, long length, int as) throws IllegalStateException;

    /**
     * maojg added at 2011-11-05 Gets the duration of the file.
     *
     * @return the duration in milliseconds
     */
    // this medthod is just useful for video record
    public native int getduration();
}
