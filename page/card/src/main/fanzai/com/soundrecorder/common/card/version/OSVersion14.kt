/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OSVersion14
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.version

import com.oplus.pantanal.seedling.constants.Constants.FluidCloudSize.NOTIFICATION_LG

class OSVersion14 : OSVersion {
    @Volatile
    private var isShowStatusBarCard: Boolean = false
    @Volatile
    private var isShowCapsule: Boolean = false

    /**
     * 14及以下方案通过卡片Size来控制
     */
    override fun onSeedlingCardSizeChanged(newSize: Int) {
        //NOTIFICATION_LG 流体胶囊展开面板形态
        isShowStatusBarCard = newSize == NOTIFICATION_LG
    }

    /**
     * 15及以上方案通过点击卡片状态来控制
     */
    override fun onSeedlingCardShowChangeWhenSave(showStatusBar: Boolean) {
        isShowCapsule = showStatusBar
    }

    override fun resetShowStatusBar() {
        isShowStatusBarCard = false
        isShowCapsule = false
    }

    override fun getShowStatusBar(): Boolean {
        return isShowStatusBarCard
    }

    override fun needHideSeedlingCard(): Boolean {
        return isShowCapsule
    }
}