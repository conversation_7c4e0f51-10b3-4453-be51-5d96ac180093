/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowsLocalLog
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.shadows

import android.content.Context
import com.oplus.recorderlog.log.RecorderLogger
import org.robolectric.annotation.Implementation
import org.robolectric.annotation.Implements

@Implements(RecorderLogger::class)
class ShadowRecorderLogger {

    @Implementation
    fun initLog(context: Context) {
        //do nothing
    }
}