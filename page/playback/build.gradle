apply from: "../../common_flavor_build.gradle"
apply plugin: 'oppo-native-dependencies'

android {
    native_dependencies {
        artifact('com.coloros.recorderlibs:libsilenceWrapper:1.0.0-arm64-v8a:arm64-v8a')
        artifact('com.coloros.recorderlibs:libSlienceDetect:1.0.1-arm64-v8a:arm64-v8a')
    }
}

dependencies {
    implementation fileTree(include: ['*.so'], dir: 'libs')
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    //kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${prop_kotlinVersion}"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-extensions:$lifecycle_extensions"
    // fragment
    implementation "androidx.fragment:fragment-ktx:$prop_fragmentVersion"
    implementation "com.google.code.gson:gson:$gson_version"
    kaptTest "androidx.databinding:databinding-compiler:7.3.1"

    // base包为必须引用的包，prop_versionName需保持一致
    implementation ("com.oplus.appcompat:core:${prop_versionName}") {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation "com.oplus.appcompat:preference:${prop_versionName}"
    implementation "com.oplus.appcompat:segmentbutton:${prop_versionName}"
    implementation "com.oplus.appcompat:viewpager:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
    implementation "com.oplus.appcompat:dialog:${prop_versionName}"
    implementation "com.oplus.appcompat:toolbar:${prop_versionName}"
    implementation "com.oplus.appcompat:chip:${prop_versionName}"
    implementation "com.oplus.appcompat:scrollbar:${prop_versionName}"
    implementation "com.oplus.appcompat:poplist:${prop_versionName}"
    implementation "com.oplus.appcompat:bottomnavigation:${prop_versionName}"
    implementation "com.oplus.appcompat:snackbar:${prop_versionName}"
    implementation "com.oplus.appcompat:button:${prop_versionName}"
    implementation "com.oplus.appcompat:seekbar:${prop_versionName}"
    implementation "com.oplus.appcompat:progressbar:${prop_versionName}"
    implementation "com.oplus.appcompat:panel:${prop_versionName}"
    implementation "com.oplus.appcompat:responsiveui:${prop_versionName}"

    implementation("com.oplus.materialcolor:coui-material-color:${coui_colorVersion}")
    compileOnly "com.oplus.sdk:addon:${prop_addonAdapterVersion}"
    testImplementation "com.oplus.sdk:addon:${prop_addonAdapterVersion}"
    domesticImplementation "com.oplus.nearx:cloudconfig-env:$prop_cloudConfigEnvVersion"
    domesticImplementation "com.oplus.nearx:cloudconfig:$prop_cloudConfigVersion"
    debugImplementation 'com.nearx.test:env:1.0.0'

    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"
    /*clodkit使用taphttp,为了解冲突，okhttp统一替换使用taphttp*/
    domesticImplementation ("com.heytap.nearx:taphttp:${prop_taphttp_version}")

    implementation project(':common:RecorderLogBase')
    testImplementation project(':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libimageload')
    implementation project(':common:libcommon')
    implementation project(':component:player')
    implementation project(':component:wavemark')
    implementation project(':component:ConvertService')
    implementation "androidx.asynclayoutinflater:asynclayoutinflater:${async_layoutinflater_version}"
    implementation project(':component:summary')
}
